import datetime

import config
import lark_requests
from log_config import logger


def from_timestamp_to_str(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M")


class Summary(lark_requests.LarkRequests):
    def __init__(self):
        super().__init__(config.APP_TOKEN, config.TABLE_ID)

    def get_vehicle_info_dict(self) -> dict:
        vehicle_info_dict = {}
        for record in self.get_all_records():
            update_flag = record.get("fields", {}).get("每次重新生成")
            if update_flag and update_flag == "否":
                continue

            vin_list = record.get("fields", {}).get("VIN")
            if vin_list is None or len(vin_list) == 0:
                logger.warning(record)
                continue

            vin = vin_list[0].get("text")
            record_id = record.get("record_id")
            if vin_list and record_id:
                if vin in vehicle_info_dict.keys():
                    # 删除重复的 VIN
                    self.delete_record(record_id)
                else:
                    tmp_dict = {
                        "fields": {},
                        "record_id": record_id,
                        "use_did": record.get("fields", {}).get("使用本地DID的软硬件零件号"),
                        "vehicle_model": record.get("fields", {}).get("车型"),
                    }
                    vehicle_info_dict[vin] = tmp_dict
            else:
                logger.warning(record)
        return vehicle_info_dict


class VehicleDetail(lark_requests.LarkRequests):
    def __init__(self):
        super().__init__(config.APP_TOKEN, config.DETAIL_TABLE_ID)

    def get_all_record_ids(self, skip_leo_flag=True) -> list:
        all_record_ids = []
        for record in self.get_all_records():
            update_flag = record.get("fields", {}).get("每次重新生成")
            if update_flag and update_flag.get("value", [""])[0] == "否":
                continue
            if skip_leo_flag and str(record.get("fields", {}).get("车型", "")).lower() == "leo":
                logger.info(f"Skip leo record: {record}")
                continue

            record_id = record.get("record_id")
            if record_id:
                all_record_ids.append(record_id)
            else:
                logger.warning(record)
        return all_record_ids


class RawEcuData(lark_requests.LarkRequests):
    def __init__(self):
        super().__init__(config.APP_TOKEN, config.RAW_ECU_TABLE_ID)

    def get_all_record_ids(self) -> list:
        all_record_ids = []
        for record in self.get_all_records():
            record_id = record.get("record_id")
            if record_id:
                all_record_ids.append(record_id)
        return all_record_ids


