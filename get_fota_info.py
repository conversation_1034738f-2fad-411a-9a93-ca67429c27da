import json
import requests
import datetime
import re
from typing import Optional

from log_config import logger


def parse_iso_time_with_nanoseconds(iso_time_str: str) -> float:
    """
    解析ISO时间字符串，处理纳秒级精度
    如果时间字符串包含超过6位小数秒，截断到6位
    """
    if not iso_time_str:
        return 0.0

    try:
        # 检查是否包含小数秒
        if '.' in iso_time_str:
            # 分离主要部分和小数秒部分
            main_part, fractional_part = iso_time_str.rsplit('.', 1)

            # 如果有时区信息，分离出来
            timezone_part = ""
            if fractional_part.endswith('Z'):
                fractional_digits = fractional_part[:-1]
                timezone_part = 'Z'
            elif '+' in fractional_part:
                fractional_digits, timezone_part = fractional_part.split('+', 1)
                timezone_part = '+' + timezone_part
            elif fractional_part.count('-') > 0:
                # 处理负时区偏移
                parts = fractional_part.split('-')
                if len(parts) > 1:
                    fractional_digits = parts[0]
                    timezone_part = '-' + '-'.join(parts[1:])
                else:
                    fractional_digits = fractional_part
            else:
                fractional_digits = fractional_part

            # 截断到最多6位小数秒（微秒级精度）
            if len(fractional_digits) > 6:
                fractional_digits = fractional_digits[:6]

            # 重新组装时间字符串
            iso_time_str = f"{main_part}.{fractional_digits}{timezone_part}"

        # 使用fromisoformat解析
        dt = datetime.datetime.fromisoformat(iso_time_str)
        return dt.timestamp()

    except Exception as e:
        logger.warning(f"Failed to parse time string '{iso_time_str}': {e}")
        # 如果解析失败，尝试使用当前时间
        return datetime.datetime.now().timestamp()


class GetFotaInfo:
    def __init__(self, cookie: str, base_url="https://fota-web-onvo-stg.nioint.com"):
        self.base_url = base_url
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": cookie,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }

    def query_vehicle_info(self, vin: str) -> Optional[dict]:
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/vehicles/query"
        self.payload = json.dumps({
            "filterRules": [{"field": "vin", "op": "contains", "value": vin}],
            "page": 1, "pageSize": 10
        })
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg") == "successfully":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict

        logger.info(response.text)
        return None

    def query_ecu_version_by_fota(self, vid: str) -> Optional[dict]:
        logger.debug(f"Querying ecu version by fota for {vid}")
        if vid:
            self.method = "POST"
            self.url = self.base_url + "/app/failuremgr/apiself/delivery/dids/query"
            self.payload = json.dumps({"vid": vid})
            response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

            if response.ok and response.status_code == 200:
                response_dict = response.json()
                if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                    iso_time_str = response_dict.get("upload_at")
                    data_list = response_dict.get("data", [])
                    if data_list and len(data_list) > 0:
                        ecu_version_map = {}
                        package_global_version = None
                        for data_dict in data_list:
                            did_key = data_dict.get("name")
                            ecu_name = data_dict.get("group")
                            did_value = data_dict.get("value", "")
                            if ecu_name is None:
                                continue
                            if ecu_name not in ecu_version_map.keys():
                                ecu_version_map[ecu_name] = {"ecu": ecu_name}

                            if did_key == "F110":
                                ecu_version_map[ecu_name]["hardware_pn"] = did_value
                            elif did_key == "F118":
                                ecu_version_map[ecu_name]["software_pn"] = did_value
                            elif did_key == "F141":
                                package_global_version = did_value

                        ecu_did = {
                            "vehicle_id": vid,
                            "sample_time": parse_iso_time_with_nanoseconds(iso_time_str),
                            "ecu_version_map": ecu_version_map,
                            "package_global_version": package_global_version,
                        }
                        return ecu_did

            logger.error(response.text)
            return None
        return None

    def send_ota_cmd(self, vids: list, cmd="didupload"):
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/otacmd/send"
        self.payload = json.dumps({"cmdType": cmd, "vids": vids})
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                return True

        logger.error(response.text)
        return False

    def vehicles_query(self):
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/vehicles/query"
        self.payload = json.dumps({"filterRules": [], "page": 1, "pageSize": 10})
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                return True

        logger.error(response.text)
        if "unauthorized" in response.text.lower():
            logger.error("Cookie expired or invalid, please login again.")

        return False
