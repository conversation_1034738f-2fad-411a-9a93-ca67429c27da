import json
import requests
import datetime
import re
from typing import Optional

from log_config import logger

# 查车辆基础信息：query_vehicle_info(vin) → 拿到 vid 和车辆基础数据。
# 查 ECU 版本：用 vid 调用 query_ecu_version_by_fota(vid) → 整理出结构化 ECU 版本字典。
# 发 OTA 指令：用 vid 列表调用 send_ota_cmd(vids, cmd) → 触发车辆执行 OTA 动作（如上传数据）。
# 校验连通性：vehicles_query() → 快速验证 Cookie 和接口是否可用。

"""
一、核心问题：Python 对时间精度的限制
Python 的 datetime.fromisoformat 方法默认支持最多 6 位小数秒（微秒级精度），但很多接口返回的时间字符串包含7-9 位小数秒（纳秒级，如 .390959441 是 9 位）。直接解析会报错，因此需要先对时间字符串进行「截断精度」处理。
二、解析步骤拆解（结合示例）
以接口返回的时间字符串 2025-08-05T15:16:55.390959441+08:00 为例，逐步说明解析过程：
1. 空值处理
如果输入的 iso_time_str 为空（None 或空字符串），直接返回 0.0，避免后续解析报错。
2. 分离「整数秒部分」和「小数秒部分」
检查时间字符串中是否包含 .（表示小数秒）。
示例中 .390959441+08:00 包含小数秒，因此通过 rsplit('.', 1) 拆分：
main_part：. 左侧的部分，即 2025-08-05T15:16:55（整数秒及之前的日期时间）。
fractional_part：. 右侧的部分，即 390959441+08:00（小数秒 + 时区信息）。
3. 提取「时区信息」和「纯小数秒」
fractional_part 可能包含时区信息（如 +08:00、-05:00 或 Z），需要分离：

情况 1：时区为 Z（UTC 时间）
若 fractional_part 以 Z 结尾（如 390959441Z），则：
fractional_digits = 390959441（去掉 Z）
timezone_part = Z（保留时区）。
情况 2：时区为 + 偏移（如 +08:00）
示例中 fractional_part 包含 +，通过 split('+', 1) 拆分：
fractional_digits = 390959441（+ 左侧的小数秒）
timezone_part = +08:00（+ 右侧的时区，拼接 + 保留）。
情况 3：时区为 - 偏移（如 -05:00）
若包含 -，通过 split('-', 1) 拆分，逻辑类似 +，最终 timezone_part 保留 -05:00。
情况 4：无时区信息
直接将 fractional_part 作为 fractional_digits，timezone_part 为空。
4. 截断小数秒至 6 位（微秒级）
Python 仅支持最多 6 位小数秒，因此对 fractional_digits 截断：
示例中 fractional_digits 是 390959441（9 位），截断为前 6 位 → 390959。
5. 重新组装时间字符串
将处理后的部分拼接，得到 Python 可解析的 ISO 时间字符串：
2025-08-05T15:16:55.390959+08:00（小数秒保留 6 位，时区完整保留）。
6. 解析为 datetime 对象并转换为时间戳
用 datetime.fromisoformat 解析组装后的字符串，得到 datetime 对象（包含时区信息）。
调用 dt.timestamp() 转换为 Unix 时间戳（自 1970-01-01 00:00:00 UTC 以来的秒数，带小数表示毫秒 / 微秒）。
"""
# 平台返回的数据是 Unix 时间戳，10 位 ： 1726651844
def parse_iso_time_with_nanoseconds(iso_time_str: str) -> float:
    """
    解析ISO时间字符串，处理纳秒级精度
    如果时间字符串包含超过6位小数秒，截断到6位
    """
    if not iso_time_str:
        return 0.0

    try:
        # 检查是否包含小数秒
        if '.' in iso_time_str:
            # 分离主要部分和小数秒部分
            main_part, fractional_part = iso_time_str.rsplit('.', 1) # 1 表示最大差分数

            # 如果有时区信息，分离出来
            timezone_part = ""
            if fractional_part.endswith('Z'):
                fractional_digits = fractional_part[:-1]
                timezone_part = 'Z'
            elif '+' in fractional_part:
                fractional_digits, timezone_part = fractional_part.split('+', 1)
                timezone_part = '+' + timezone_part
            elif fractional_part.count('-') > 0:
                # 处理负时区偏移
                parts = fractional_part.split('-')
                if len(parts) > 1:
                    fractional_digits = parts[0]
                    timezone_part = '-' + '-'.join(parts[1:])
                else:
                    fractional_digits = fractional_part
            else:
                fractional_digits = fractional_part

            # 截断到最多6位小数秒（微秒级精度）
            if len(fractional_digits) > 6:
                fractional_digits = fractional_digits[:6]

            # 重新组装时间字符串
            iso_time_str = f"{main_part}.{fractional_digits}{timezone_part}"

        # 使用fromisoformat解析
        dt = datetime.datetime.fromisoformat(iso_time_str)
        return dt.timestamp()

    except Exception as e:
        logger.warning(f"Failed to parse time string '{iso_time_str}': {e}")
        # 如果解析失败，尝试使用当前时间
        return datetime.datetime.now().timestamp()


class GetFotaInfo:
    def __init__(self, cookie: str, base_url="https://fota-web-onvo-stg.nioint.com"):
        self.base_url = base_url
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": cookie,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
    """
    query_vehicle_info：查询车辆基础信息
    通过车辆 vin 码，调用 FOTA 平台的 /app/failuremgr/apiself/delivery/vehicles/query 接口，获取车辆在 FOTA 系统中的基础信息（如 vid、platform、cintversion 等）。
    请求构造：
        method：POST
        url：拼接 base_url 和接口路径 /app/failuremgr/apiself/delivery/vehicles/query
        payload：构造 JSON 请求体，用 filterRules 按 vin 模糊匹配（contains），指定页码 page:1、每页条数 pageSize:10
    {
    "filterRules": [{"field": "vin", "op": "contains", "value": "HJNNAHSC1SC000015"}],
    "page": 1, 
    "pageSize": 10
    }

    {
    "retcode": 0,
    "msg": "successfully",
    "total": 1,
    "data": [
        {
        "vid": "1ad6e6287c3747571326768680004010",
        "vin": "HJNNAHSC1SC000015",
        "platform": "Cetus",
        "cintversion": "V0085511 AI",
        "cextversion": "Cetus.G1.1.AE.01_1k39dz*0.8.3",
        // ... 其他字段 ...
        }
    ]
    }
    """
    def query_vehicle_info(self, vin: str) -> Optional[dict]:
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/vehicles/query"
        self.payload = json.dumps({
            "filterRules": [{"field": "vin", "op": "contains", "value": vin}],
            "page": 1, "pageSize": 10
        })
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg") == "successfully":
                data_list = response_dict.get("data")
                if data_list and len(data_list) > 0:
                    data_dict = data_list[0]
                    return data_dict

        logger.info(response.text)
        return None

    """
    二、query_ecu_version_by_fota：查询车辆 ECU 版本信息
    1. 功能
    用 query_vehicle_info 拿到的 vid ，调用 /app/failuremgr/apiself/delivery/dids/query 接口，解析并整理车辆 ECU 模块的软硬件版本、整车软件包版本等数据。
    2. 数据获取流程
    请求构造：
        method：POST
        url：拼接 base_url 和接口路径 /app/failuremgr/apiself/delivery/dids/query
        payload：构造 JSON 请求体，传入 vid（来自 query_vehicle_info 的返回 ）
    {"vid": "1ad6e6287c3747571326768680004010"}
    解析时间：用 parse_iso_time_with_nanoseconds 处理接口返回的 upload_at 时间字符串，转成时间戳。
    整理 ECU 数据：
    遍历 response_dict["data"]（ECU 相关 DID 数据列表 ），按 group（ECU 模块名，如 HVC、PEU_R 等）分组。
    根据 name（DID 标识，如 F110、F118 等）提取对应值：
    F110 → hardware_pn（硬件版本）
    F118 → software_pn（软件版本）
    F141 → package_global_version（整车软件包版本）
    
    最终构造一个字典 ecu_did ，包含：

    {
    "vehicle_id": vid,
    "sample_time": 时间戳,
    "ecu_version_map": {
        "HVC": {"ecu": "HVC", "hardware_pn": "P0381345 AF", "software_pn": "P0362459 AN"},
        "PEU_R": {"ecu": "PEU_R", "hardware_pn": "P0388994 AD", "software_pn": "P0397610 AL"},
        // ... 其他 ECU 模块 ...
    },
    "package_global_version": "ES7.G1.3.AJ.01_6aeea1*3.2.0build34"  // 示例值
    }

    示例响应（简化 data 部分）：
    {
    "retcode": 0,
    "msg": "Successfully",
    "upload_at": "2025-08-05T15:16:55.390959441+08:00",
    "data": [
        {"name": "F18C", "value": "S0S43X4N00JYN", "group": "HVC", "validate": true},
        {"name": "F110", "value": "P0381345 AF", "group": "HVC", "validate": true},
        {"name": "F118", "value": "P0362459 AN", "group": "HVC", "validate": true},
        // ... 大量 ECU 模块数据 ...
    ]
    }
    """

    def query_ecu_version_by_fota(self, vid: str) -> Optional[dict]:
        logger.debug(f"Querying ecu version by fota for {vid}")
        if vid:
            self.method = "POST"
            self.url = self.base_url + "/app/failuremgr/apiself/delivery/dids/query"
            self.payload = json.dumps({"vid": vid})
            response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

            if response.ok and response.status_code == 200:
                response_dict = response.json()
                if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                    iso_time_str = response_dict.get("upload_at")
                    data_list = response_dict.get("data", [])
                    if data_list and len(data_list) > 0:
                        ecu_version_map = {}
                        package_global_version = None
                        for data_dict in data_list:
                            did_key = data_dict.get("name")
                            ecu_name = data_dict.get("group")
                            did_value = data_dict.get("value", "")
                            if ecu_name is None:
                                continue
                            if ecu_name not in ecu_version_map.keys():
                                ecu_version_map[ecu_name] = {"ecu": ecu_name}

                            if did_key == "F110":
                                ecu_version_map[ecu_name]["hardware_pn"] = did_value
                            elif did_key == "F118":
                                ecu_version_map[ecu_name]["software_pn"] = did_value
                            elif did_key == "F141":
                                package_global_version = did_value

                        ecu_did = {
                            "vehicle_id": vid,
                            "sample_time": parse_iso_time_with_nanoseconds(iso_time_str),
                            "ecu_version_map": ecu_version_map,
                            "package_global_version": package_global_version,
                        }
                        return ecu_did

            logger.error(response.text)
            return None
        return None

    """
    三、send_ota_cmd：发送 OTA 指令
    1. 功能
    调用 /app/failuremgr/apiself/delivery/otacmd/send 接口，向指定 vid 列表的车辆发送 OTA 指令（如 didupload ），触发车辆执行升级、数据上传等操作。
    2. 数据流程
    请求构造：  
        method：POST
        url：拼接 base_url 和接口路径 /app/failuremgr/apiself/delivery/otacmd/send
        payload：构造 JSON 请求体，传入指令类型 cmdType（如 didupload ）和车辆 vids 列表
    {
    "cmdType": "didupload", 
    "vids": ["0b55b748225542e81347393090201010"]
    }

    {
    "retcode": 0,
    "msg": "Successfully",
    "cmdType": "didupload",
    "vids": ["0b55b748225542e81347393090201010"],
    // ... 其他指令执行信息 ...
    }
    """

    def send_ota_cmd(self, vids: list, cmd="didupload"):
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/otacmd/send"
        self.payload = json.dumps({"cmdType": cmd, "vids": vids})
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                return True

        logger.error(response.text)
        return False

    """
    四、vehicles_query：查询车辆列表（简化版）
    1. 功能
    调用 /app/failuremgr/apiself/delivery/vehicles/query 接口，查询 FOTA 系统中符合条件（无 filterRules 时查全量）的车辆列表，主要用于校验 Cookie 有效性或获取车辆概览。
    2. 数据流程
    请求构造：
    method：POST
    url：拼接 base_url 和接口路径 /app/failuremgr/apiself/delivery/vehicles/query
    payload：构造 JSON 请求体，空 filterRules、page:1、pageSize:10

    {"filterRules": [], "page": 1, "pageSize": 10}

    {
    "retcode": 0,
    "msg": "successfully",
    "total": 1173,
    "data": [
        {"vid": "6e74fddf7e9348a91729213650101010", "platform": "DOM", ...},
        {"vid": "4777e9ed786b43b11361819950101010", "vin": "PANGU088914403453", ...},
        // ... 大量车辆数据 ...
    ]
    }
    """

    def vehicles_query(self):
        self.method = "POST"
        self.url = self.base_url + "/app/failuremgr/apiself/delivery/vehicles/query"
        self.payload = json.dumps({"filterRules": [], "page": 1, "pageSize": 10})
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("retcode") == 0 and response_dict.get("msg").lower() == "successfully":
                return True

        logger.error(response.text)
        if "unauthorized" in response.text.lower():
            logger.error("Cookie expired or invalid, please login again.")

        return False
