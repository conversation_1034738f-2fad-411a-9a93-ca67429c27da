import json
import requests
import time

from log_config import logger

"""
车型 → 发布计划：
    用 get_vehicle_release(vehicle="ET5 G1.2.E") 获取该车型的所有发布计划，得到 release_plan_id（如 918）。
发布计划 → 软件包：
    用 get_package_list(release_plan_id="918") 获取该计划下的软件包，得到 package_id（如 8632）。
软件包 → ECU 配置：
    用 get_package_detail(package_id="8632") 获取该软件包包含的 ECU 配置，得到 swPn（如 P0280835）。
软件零件号 → 版本历史：
    用 get_revision_list(sw_pn="P0280835") 追溯该 ECU 软件的所有修订记录，关联历史变更。
离线归档：
    用 download_bom(package_id="8632") 下载 Excel，保存当前配置的完整 BOM 清单。
"""

class GetPhoenixInfo:

    def __init__(self, cookie: dict):
        self.method = ""
        self.url = ""
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
        self.payload = {}

    """
    get_revision_list(sw_pn: str)：获取 ECU 软件的版本修订历史
    功能：根据软件零件号（sw_pn）查询该 ECU（电子控制单元）的所有版本修订记录，包含修订号、发布范围、状态等信息。

    请求构造：
        接口：https://phoenix.nioint.com/nodecfr/api/v1/ecu/revision/list（POST 方法）。
        请求体：{"pn": sw_pn}（如 {"pn": "P0271673"}）。
        headers：包含 Cookie（身份验证）、User-Agent（浏览器标识）、Content-Type: application/json（JSON 格式请求体）。
    响应解析（以 sw_pn="P0271673" 为例）：
    返回 data 列表，每个元素代表一个修订版本，核心字段包括：

       "data": [
        {
            "id": "0",
            "key": "FR-39719",
            "ecu": "ADC",
            "category": "EU",
            "swType": "EU",
            "swPn": "P0271673",
            "swRevision": "AA",
            "releaseScope": "ET7 G1.1.E ET7.G1.1.E.AA.01 BL0.9.0",
            "releaseDate": "2022-05-06",
            "status": "In Release",
            "swPackage": "true",
            "dbcVersion": "v1.0.0",
            "notes": "ADMS\t\nSuppress both MD and DA;\nCancel suppression condition of steering wheel angle;\nAdd suppression condition from turn indicator\n\nELK\t\nAdd logic for RHD markets\n\nOthers\t\nChange the interface between VCU and ADC\n",
            "packMethod": "excel",
            "edms": "1",
            "specialPn": true,
            "buildingSource": ""
        }
    ]
    """

    def get_revision_list(self, sw_pn: str):
        self.method = "POST"
        self.url = "https://phoenix.nioint.com/nodecfr/api/v1/ecu/revision/list"
        data_dict = {"pn": sw_pn}
        self.payload = json.dumps(data_dict)
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            data_list = response_dict.get("data")
            if data_list and len(data_list) > 0:
                return data_list
            else:
                logger.error(f"Failed to get revision list for {sw_pn}!")
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    """
    get_vehicle_release(vehicle: str)：获取特定车型的发布计划
    功能：根据车型（如 ET5 G1.2.E）查询该车型的所有软件发布计划，包含计划时间、版本号、状态等。

    请求构造：
    接口：https://phoenix.nioint.com/nodefr/api/v1/release/plan（GET 方法）。
    URL 参数：
        vehicle：车型（如 ET5+G1.2.E，需 URL 编码）。
        page=1&limit=100：分页参数（默认第 1 页，最多 100 条）。
        _t=timestamp：毫秒级时间戳（防止缓存，确保实时性）。

    {
    "code": 200,
    "data": {
        "meta": {
            "total": "9"
        },
        "items": [
            {
                "id": "1003",
                "key": "FR-891855",
                "platform": "NT2",
                "vehicle": "ET5 G1.2.E",
                "os": "BL2.3.7",
                "swPackageEngineerVersion": "ET5.G1.2.E.AG.01",
                "swBaselineConfigDate": "2025-05-16",
                "swConfigFrozenDate": "2025-08-01",
                "plmReleaseDate": "2025-08-08",
                "releaseToUser": "2025-08-08",
                "swVariantPn": "V0082347",
                "swVariantRev": "",
                "majorRev": "BL2.3.7",
                "minorRev": "",
                "status": "In Validation",
                "note": "GB260",
                "enabled": true,
                "releaseConfirm": true,
                "taskStatus": "",
                "quickTestVfiTime": "",
                "autoFuncVfiTime": ""
            },

        函数提取 items 列表返回，关键信息 id 用于关联后续的软件包列表。
    """

    def get_vehicle_release(self, vehicle: str):
        timestamp = int(time.time() * 1000)
        self.url = \
            f"https://phoenix.nioint.com/nodefr/api/v1/release/plan?page=1&limit=100&vehicle={vehicle}&_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 200:
                items = response_dict.get("data", {}).get("items")
                if items and len(items) > 0:
                    return items
                else:
                    logger.error(f"No release info for {vehicle}!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    """
    get_package_list(release_plan_id: str)：获取发布计划下的软件包列表
    功能：根据发布计划 ID（release_plan_id）查询该计划包含的所有软件包（构建版本），包含构建时间、状态、基线版本等。
    请求构造：
    接口：https://phoenix.nioint.com/nodefr/api/v1/release/plan/{release_plan_id}/build（GET 方法）。
    URL 参数：_t=timestamp（防缓存）。

    {
    "code": 200,
    "data": [
        {
            "id": "8627",
            "platform": "NT2",
            "vehicle": "ET5 G1.2.E",
            "os": "BL2.3.6",
            "name": "NT2-ET5 G1.2.E-BL2.3.6-1",
            "pkgPlan": {
                "id": "7507",
                "name": "Sprint1",
                "autoBuild": false
            },
            "startTime": "2024-11-13 10:18:16",
            "endTime": "1970-01-01 08:00:01",
            "status": "In Validation",
            "swBaseline": "NT2-ET5 G1.2.E-BL2.3.5-29",
            "tag": "",
            "swVariantPn": "V0082347",
            "swVariantRev": "",
            "autoBuild": false,
            "bomCnt": 0,
            "execMethod": "manual",
            "buildUser": "frank.zhang3",
            "notifyTime": "2024-11-13 10:18:24",
            "releasePlanId": "918"
        },
    """

    def get_package_list(self, release_plan_id: str):
        timestamp = int(time.time() * 1000)
        self.url = \
            f"https://phoenix.nioint.com/nodefr/api/v1/release/plan/{release_plan_id}/build?_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 200:
                items = response_dict.get("data")
                if items and len(items) > 0:
                    return items
                else:
                    logger.error(f"No release info for {release_plan_id}!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    """
    get_package_detail(package_id: str)：获取软件包的 ECU 软硬件配置
    功能：根据软件包 ID（package_id）查询该软件包包含的所有 ECU 模块的软硬件配置，包含零件号、修订号、硬件兼容列表等。
    请求构造：
    接口：https://phoenix.nioint.com/nodefr/api/v1/release/build/{package_id}/bom（GET 方法）。
    URL 参数：_t=timestamp（防缓存）。

     "code": 200,
    "data": [
        {
            "id": "608459",
            "ecuMark": "ACM",
            "subCategory": "EU",
            "swPn": "P0280835",
            "swRevision": "AE",
            "prevSwRevision": "",
            "changeStatus": "",
            "summary": "Airbag Control Unit  - SW",
            "swKey": "FR-293222",
            "swPackageName": "",
            "swPackageUrl": "",
            "hwList": [
                {
                    "pn": "P0064885",
                    "vehicle": "ET5 G1.2.E",
                    "summary": "",
                    "revisions": [
                        {
                            "key": "",
                            "name": "AH",
                            "specialPn": [],
                            "changeStatus": 0
                        },
                        {
                            "key": "",
                            "name": "AI",
                            "specialPn": [],
                            "changeStatus": 0
                        }
                    ]
                },
        函数返回 data 列表，完整保留 ECU 的软硬件关联关系，是分析配置兼容性的核心数据。
    """

    def get_package_detail(self, package_id: str):
        timestamp = int(time.time() * 1000)
        self.url = f"https://phoenix.nioint.com/nodefr/api/v1/release/build/{package_id}/bom?_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 200:
                items = response_dict.get("data")
                if items and len(items) > 0:
                    return items
                else:
                    logger.error(f"No release info for {package_id}!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    """
    download_bom(package_id)：下载软件包的 BOM 清单（Excel）
    功能：根据软件包 ID（package_id）下载包含所有 ECU 配置的 Excel 文件（BOM：物料清单），便于离线查看和归档。
    请求构造：
    接口：https://phoenix.nioint.com/nodefr/api/v1/release/build/{package_id}/excel/download（GET 方法）。
    URL 参数：_t=timestamp（防缓存）。
    响应处理：
    接口返回二进制 Excel 文件流，函数直接返回 response 对象，需调用方进一步处理（如保存为本地文件）：（后续需要更改）
    """

    def download_bom(self, package_id):
        timestamp = int(time.time() * 1000)
        self.url = \
            f"https://phoenix.nioint.com/nodefr/api/v1/release/build/{package_id}/excel/download?_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            logger.debug(f"Get {package_id} package detail.")
            return response

        logger.error(f"Failed to download {package_id} package detail!")
        logger.error(response.text)
        return None

'''
在 API 请求中添加timestamp（时间戳）是一种常见的技术手段，尤其在接口设计中具有明确的作用。结合你提供的代码和接口场景，具体原因及合理性分析如下：
为什么需要添加 timestamp？
timestamp = int(time.time() * 1000) 生成的是毫秒级当前时间戳（随时间动态变化），其核心作用是：
防止请求缓存（核心原因）
许多服务器（尤其是静态资源服务器或 CDN）会对重复的 URL 请求进行缓存，以提高响应速度。如果两次请求的 URL 完全相同，服务器可能直接返回缓存结果，而非实时数据。
对于车辆发布计划这类动态更新的数据（如状态从 “开发中” 变为 “已发布”），缓存会导致客户端获取到旧数据，影响准确性。
添加动态变化的timestamp后，每次请求的 URL 都不同（如_t=1754458892109和_t=1754458900123），服务器会认为是新请求，从而返回实时数据。
防重放攻击（安全层面）
部分接口会通过时间戳验证请求的时效性，防止攻击者重复使用截获的请求（“重放攻击”）。
服务器可校验timestamp是否在有效时间窗口内（如 5 分钟内），若超出则拒绝请求。
虽然你的代码中未体现服务器的校验逻辑，但添加时间戳是行业内通用的安全最佳实践，尤其适用于涉及车辆配置的敏感接口。
确保请求唯一性
在高并发场景下，多个相同参数的请求可能同时发送，服务器可能因无法区分而导致处理异常。
时间戳的唯一性可帮助服务器识别不同请求，避免混淆。

为什么动态变化的 timestamp 能正常访问？
这是由服务器的接口设计逻辑决定的：
 服务器对 timestamp 的处理逻辑
服务器在接收到带timestamp的请求时，通常不会强制要求其为某个固定值，而是：
忽略timestamp字段（仅用于客户端防缓存）；
或验证其是否在合理范围内（如不早于服务器当前时间，或不晚于未来 5 分钟），只要有效就正常处理。
例如你的接口https://phoenix.nioint.com/nodefr/api/v1/release/plan，服务器的核心逻辑是根据vehicle、page等参数返回数据，_t仅作为 “防缓存标识”，不影响业务逻辑。

动态变化的timestamp是通过确保请求唯一性和避免缓存来保障接口访问的有效性，服务器对其的处理逻辑（忽略或时效校验）决定了它不会影响正常请求，反而能提升数据的实时性和安全性。这是接口开发中针对动态数据的标准化设计，广泛应用于各类需要实时性的场景。
'''