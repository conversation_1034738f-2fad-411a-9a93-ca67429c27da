import json
import requests
import time

from log_config import logger


class GetPhoenixInfo:

    def __init__(self, cookie: dict):
        self.method = ""
        self.url = ""
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json"
        }
        self.payload = {}

    def get_revision_list(self, sw_pn: str):
        self.method = "POST"
        self.url = "https://phoenix.nioint.com/nodecfr/api/v1/ecu/revision/list"
        data_dict = {"pn": sw_pn}
        self.payload = json.dumps(data_dict)
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            data_list = response_dict.get("data")
            if data_list and len(data_list) > 0:
                return data_list
            else:
                logger.error(f"Failed to get revision list for {sw_pn}!")
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    def get_vehicle_release(self, vehicle: str):
        timestamp = int(time.time() * 1000)
        self.url = \
            f"https://phoenix.nioint.com/nodefr/api/v1/release/plan?page=1&limit=100&vehicle={vehicle}&_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 200:
                items = response_dict.get("data", {}).get("items")
                if items and len(items) > 0:
                    return items
                else:
                    logger.error(f"No release info for {vehicle}!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    def get_package_list(self, release_plan_id: str):
        timestamp = int(time.time() * 1000)
        self.url = \
            f"https://phoenix.nioint.com/nodefr/api/v1/release/plan/{release_plan_id}/build?_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 200:
                items = response_dict.get("data")
                if items and len(items) > 0:
                    return items
                else:
                    logger.error(f"No release info for {release_plan_id}!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    def get_package_detail(self, package_id: str):
        timestamp = int(time.time() * 1000)
        self.url = f"https://phoenix.nioint.com/nodefr/api/v1/release/build/{package_id}/bom?_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 200:
                items = response_dict.get("data")
                if items and len(items) > 0:
                    return items
                else:
                    logger.error(f"No release info for {package_id}!")
                    return None
            else:
                logger.error(response_dict)
                return None
        else:
            logger.error(response.text)
            return None

    def download_bom(self, package_id):
        timestamp = int(time.time() * 1000)
        self.url = \
            f"https://phoenix.nioint.com/nodefr/api/v1/release/build/{package_id}/excel/download?_t={timestamp}"
        response = requests.request("GET", self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            logger.debug(f"Get {package_id} package detail.")
            return response

        logger.error(f"Failed to download {package_id} package detail!")
        logger.error(response.text)
        return None
