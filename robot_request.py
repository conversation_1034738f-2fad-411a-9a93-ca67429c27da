import requests
import json
import time

import hashlib
import base64
import hmac

import config
from log_config import logger


def gen_sign(timestamp, secret):
    # 拼接timestamp和secret
    string_to_sign = "{}\n{}".format(timestamp, secret)
    hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()

    # 对结果进行base64处理
    sign = base64.b64encode(hmac_code).decode("utf-8")

    return sign


class RebotRequest:

    def __init__(self):
        self.url = config.WEBHOOK

        self.headers = {"Content-Type": "application/json"}

        self.payload = json.dumps({})

    def set_payload(self, title, paragraph):
        timestamp_int = int(time.time())
        sign_secret = config.SECRET
        coded_sign = gen_sign(timestamp_int, sign_secret)

        self.payload = json.dumps(
            {
                "timestamp": timestamp_int,
                "sign": coded_sign,
                "msg_type": "post",
                "content": {
                    "post": {
                        "zh_cn": {
                            "title": title,
                            "content": [
                                [
                                    {
                                        "tag": "text",
                                        "text": paragraph,
                                    }
                                ],
                            ]
                        }
                    }
                }
            }
        )

    def request(self):
        response = requests.request("POST", self.url, headers=self.headers, data=self.payload)
        response_dict = response.json()

        if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
            logger.debug(f"Robot request, ok.")
            """
            {
                 "StatusCode": 0,               //冗余字段，用于兼容存量历史逻辑，不建议使用
                 "StatusMessage": "success",    //冗余字段，用于兼容存量历史逻辑，不建议使用
                 "code": 0,
                 "data": {},
                 "msg": "success"
            }
            """
            return True
        else:
            logger.error(f"Failed to send a message by feishu robot!")
            logger.error(response_dict)
            return False
