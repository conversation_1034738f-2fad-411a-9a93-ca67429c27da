import requests
import json
import time

import hashlib
import base64
import hmac

# 导入配置文件（包含Webhook地址和密钥等信息）
import config
# 导入日志配置（用于记录操作日志）
from log_config import logger


def gen_sign(timestamp, secret):
    """
    生成飞书API要求的签名
    
    飞书机器人消息发送需要进行签名验证，确保请求来自合法来源。
    签名生成规则：使用HMAC-SHA256算法对时间戳和密钥的拼接字符串进行加密，
    再对加密结果进行Base64编码。
    
    参数:
        timestamp: 时间戳（秒级）
        secret: 飞书机器人的密钥
        
    返回:
        生成的签名字符串
    """
    # 拼接timestamp和secret，格式为"{timestamp}\n{secret}"
    string_to_sign = "{}\n{}".format(timestamp, secret)
    # 使用HMAC-SHA256算法计算哈希值
    hmac_code = hmac.new(
        string_to_sign.encode("utf-8"),  # 待加密的字符串（需转为字节流）
        digestmod=hashlib.sha256         # 加密算法
    ).digest()

    # 对哈希结果进行base64编码并转为字符串
    sign = base64.b64encode(hmac_code).decode("utf-8")

    return sign


class RebotRequest:
    """飞书机器人请求类，用于构建和发送飞书机器人消息"""

    def __init__(self):
        """初始化飞书机器人请求对象"""
        # 从配置文件获取Webhook地址（飞书机器人的消息接收地址）
        self.url = config.WEBHOOK

        # 设置请求头，指定内容类型为JSON
        self.headers = {"Content-Type": "application/json"}

        # 初始化请求体（默认为空JSON）
        self.payload = json.dumps({})

    def set_payload(self, title, paragraph):
        """
        构建消息请求体
        
        参数:
            title: 消息标题
            paragraph: 消息内容段落
        """
        # 获取当前时间戳（秒级）
        timestamp_int = int(time.time())
        # 从配置文件获取密钥
        sign_secret = config.SECRET
        # 生成签名
        coded_sign = gen_sign(timestamp_int, sign_secret)

        # 构建符合飞书"post"类型消息格式的请求体
        self.payload = json.dumps(
            {
                "timestamp": timestamp_int,  # 时间戳
                "sign": coded_sign,          # 签名
                "msg_type": "post",          # 消息类型为富文本
                "content": {
                    "post": {
                        "zh_cn": {           # 中文内容
                            "title": title,  # 消息标题
                            "content": [      # 消息内容
                                [
                                    {
                                        "tag": "text",       # 文本类型
                                        "text": paragraph,   # 文本内容
                                    }
                                ],
                            ]
                        }
                    }
                }
            }
        )

    def request(self):
        """
        发送消息请求
        
        返回:
            布尔值：True表示发送成功，False表示发送失败
        """
        try:
            # 发送POST请求
            response = requests.request(
                "POST", 
                self.url, 
                headers=self.headers, 
                data=self.payload
            )
            # 解析响应为字典
            response_dict = response.json()

            # 检查响应状态（飞书API成功返回code=0且msg="success"）
            if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
                logger.debug(f"Robot request, ok.")
                """
                成功响应示例：
                {
                     "StatusCode": 0,               // 冗余字段，兼容历史逻辑
                     "StatusMessage": "success",    // 冗余字段，兼容历史逻辑
                     "code": 0,
                     "data": {},
                     "msg": "success"
                }
                """
                return True
            else:
                logger.error(f"Failed to send a message by feishu robot!")
                logger.error(response_dict)
                return False
        except Exception as e:
            logger.error(f"Error sending message: {str(e)}")
            return False
    