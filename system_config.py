#!/usr/bin/env python3
"""
系统配置文件 - 针对Ubuntu i7-7700 16GB内存优化
"""

import os
import psutil

# 系统硬件信息
SYSTEM_INFO = {
    'cpu_model': 'Intel(R) Core(TM) i7-7700 CPU @ 3.60GHz',
    'cpu_cores': 4,
    'cpu_threads': 8,
    'memory_total': '16GB',
    'os': 'Ubuntu'
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    # 并发配置 - 针对i7-7700 4核8线程优化
    'max_workers': {
        'default': 8,           # 默认并发数，适合网络I/O密集型任务
        'cpu_intensive': 4,     # CPU密集型任务，等于物理核心数
        'io_intensive': 12,     # I/O密集型任务，可以更高
        'min_workers': 4,       # 最小并发数
        'max_workers': 16       # 最大并发数限制
    },
    
    # 缓存配置 - 针对16GB内存优化
    'cache_sizes': {
        'vehicle_release': 512,      # 车型版本缓存
        'package_list': 256,         # 软件包列表缓存
        'package_detail': 128,       # 软件包详情缓存
        'vfr_list': 64,             # VFR列表缓存
        'version_compare': 2048,     # 版本比较缓存（频繁使用）
        'pn_validation': 4096,       # PN验证缓存（数量多）
        'debug_package': 1024,       # 调试包检查缓存
        'messy_code': 4096,         # 乱码检查缓存
        'vehicle_model': 256,        # 车型匹配缓存
        'nt25_vehicle': 128         # NT2.5车型判断缓存
    },
    
    # 内存使用配置
    'memory': {
        'max_cache_memory_mb': 512,  # 最大缓存内存使用（MB）
        'batch_size': 100,           # 批处理大小
        'gc_threshold': 1000         # 垃圾回收阈值
    },
    
    # 网络请求配置
    'network': {
        'timeout': 30,               # 请求超时时间（秒）
        'retry_times': 3,            # 重试次数
        'retry_delay': 1,            # 重试延迟（秒）
        'connection_pool_size': 20   # 连接池大小
    }
}

def get_optimal_workers(task_count: int, task_type: str = 'default') -> int:
    """
    根据任务数量和类型获取最优并发数
    
    Args:
        task_count: 任务总数
        task_type: 任务类型 ('default', 'cpu_intensive', 'io_intensive')
    
    Returns:
        最优并发线程数
    """
    config = PERFORMANCE_CONFIG['max_workers']
    
    if task_type == 'cpu_intensive':
        base_workers = config['cpu_intensive']
    elif task_type == 'io_intensive':
        base_workers = config['io_intensive']
    else:
        base_workers = config['default']
    
    # 根据任务数量动态调整
    if task_count < 10:
        optimal = min(task_count, config['min_workers'])
    elif task_count < 50:
        optimal = min(base_workers, task_count // 2)
    else:
        optimal = base_workers
    
    # 确保在合理范围内
    optimal = max(config['min_workers'], min(optimal, config['max_workers']))
    
    return optimal

def get_cache_size(cache_type: str) -> int:
    """
    获取指定类型的缓存大小
    
    Args:
        cache_type: 缓存类型
    
    Returns:
        缓存大小
    """
    return PERFORMANCE_CONFIG['cache_sizes'].get(cache_type, 256)

def check_system_resources():
    """
    检查系统资源使用情况
    
    Returns:
        系统资源信息字典
    """
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'cpu_usage': cpu_percent,
            'memory_total': memory.total // (1024**3),  # GB
            'memory_used': memory.used // (1024**3),    # GB
            'memory_percent': memory.percent,
            'disk_total': disk.total // (1024**3),      # GB
            'disk_used': disk.used // (1024**3),        # GB
            'disk_percent': (disk.used / disk.total) * 100
        }
    except ImportError:
        # 如果psutil不可用，返回默认值
        return {
            'cpu_usage': 0,
            'memory_total': 16,
            'memory_used': 8,
            'memory_percent': 50,
            'disk_total': 100,
            'disk_used': 50,
            'disk_percent': 50
        }

def print_system_info():
    """打印系统配置信息"""
    print("=== 系统配置信息 ===")
    print(f"CPU: {SYSTEM_INFO['cpu_model']}")
    print(f"核心数: {SYSTEM_INFO['cpu_cores']} 核 {SYSTEM_INFO['cpu_threads']} 线程")
    print(f"内存: {SYSTEM_INFO['memory_total']}")
    print(f"操作系统: {SYSTEM_INFO['os']}")
    
    print("\n=== 性能优化配置 ===")
    print(f"默认并发数: {PERFORMANCE_CONFIG['max_workers']['default']}")
    print(f"最大缓存内存: {PERFORMANCE_CONFIG['memory']['max_cache_memory_mb']}MB")
    print(f"批处理大小: {PERFORMANCE_CONFIG['memory']['batch_size']}")
    
    resources = check_system_resources()
    print(f"\n=== 当前资源使用 ===")
    print(f"CPU使用率: {resources['cpu_usage']:.1f}%")
    print(f"内存使用: {resources['memory_used']}/{resources['memory_total']}GB ({resources['memory_percent']:.1f}%)")
    print(f"磁盘使用: {resources['disk_used']}/{resources['disk_total']}GB ({resources['disk_percent']:.1f}%)")

def get_recommended_settings(vehicle_count: int):
    """
    根据车辆数量推荐最佳设置
    
    Args:
        vehicle_count: 车辆数量
    
    Returns:
        推荐设置字典
    """
    # 根据车辆数量调整设置
    if vehicle_count < 50:
        workers = get_optimal_workers(vehicle_count, 'default')
        batch_size = min(20, vehicle_count)
    elif vehicle_count < 200:
        workers = get_optimal_workers(vehicle_count, 'io_intensive')
        batch_size = 50
    else:
        workers = PERFORMANCE_CONFIG['max_workers']['max_workers']
        batch_size = 100
    
    return {
        'max_workers': workers,
        'batch_size': batch_size,
        'estimated_time': vehicle_count / workers * 2,  # 估算时间（秒）
        'memory_usage_mb': workers * 50 + batch_size * 2  # 估算内存使用
    }

if __name__ == "__main__":
    print_system_info()
    
    # 测试不同车辆数量的推荐设置
    test_counts = [10, 50, 100, 500, 1000]
    print(f"\n=== 不同车辆数量的推荐设置 ===")
    for count in test_counts:
        settings = get_recommended_settings(count)
        print(f"{count:4d} 辆车: 并发数={settings['max_workers']:2d}, "
              f"批处理={settings['batch_size']:3d}, "
              f"预计时间={settings['estimated_time']:6.1f}秒, "
              f"内存={settings['memory_usage_mb']:3d}MB")
