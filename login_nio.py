from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from fake_useragent import UserAgent
import time
import sys

import config
from log_config import logger


class LoginNio:
    def __init__(self, url: str, element: str):
        if len(config.USER) == 0 or len(config.PASSWORD) == 0:
            logger.error(f"请先补全 {url} 页面的登录信息，至 {config} 文件!")
            exit()

        self.url = url
        self.element_id = element

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument(f"--user-agent={UserAgent().chrome}")

        # 基础稳定性选项
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--start-maximized')

        # 禁用自动化检测
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.page_load_strategy = 'eager'

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.implicitly_wait(10)
        self.wait = WebDriverWait(self.driver, 20)
        self.__login()

    def _apply_python312_fix(self):
        """专门针对Python 3.12的缩放修复"""
        try:
            # 检测页面是否过大
            page_info = self.driver.execute_script("""
                return {
                    windowHeight: window.innerHeight,
                    documentHeight: document.documentElement.scrollHeight,
                    windowWidth: window.innerWidth,
                    documentWidth: document.documentElement.scrollWidth
                };
            """)
            
            # 如果页面明显过大，应用修复
            height_ratio = page_info['documentHeight'] / page_info['windowHeight']
            width_ratio = page_info['documentWidth'] / page_info['windowWidth']
            
            if height_ratio > 1.3 or width_ratio > 1.1:
                logger.info(f"检测到Python 3.12缩放问题 (高度比例: {height_ratio:.2f}, 宽度比例: {width_ratio:.2f})")
                
                # 应用缩放修复
                scale = 0.75 if height_ratio > 1.5 else 0.85
                self.driver.execute_script(f"""
                    // 应用缩放修复
                    document.body.style.zoom = '{scale}';
                    document.documentElement.style.zoom = '{scale}';
                    document.body.style.transform = 'scale({scale})';
                    document.body.style.transformOrigin = '0 0';
                    
                    // 创建CSS样式确保修复生效
                    var style = document.createElement('style');
                    style.innerHTML = `
                        html, body {{
                            zoom: {scale} !important;
                            transform: scale({scale}) !important;
                            transform-origin: 0 0 !important;
                        }}
                    `;
                    document.head.appendChild(style);
                """)
                time.sleep(1)
                logger.info(f"已应用 {scale} 倍缩放修复")
                return True
            else:
                logger.debug("页面显示正常，无需缩放修复")
                return False
        except Exception as e:
            logger.debug(f"缩放检测失败: {e}")
            return False

    def _smart_find_element(self, by, value, timeout=15):
        """智能元素查找，处理Python 3.12兼容性问题"""
        
        # 方法1: 直接查找
        try:
            element = WebDriverWait(self.driver, 3).until(
                EC.presence_of_element_located((by, value))
            )
            if element.is_displayed():
                return element
        except TimeoutException:
            pass
        
        # 方法2: 应用缩放修复后查找
        if self._apply_python312_fix():
            try:
                element = WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((by, value))
                )
                if element.is_displayed():
                    return element
            except TimeoutException:
                pass
        
        # 方法3: 使用替代选择器
        if by == By.ID:
            alternatives = []
            if value == "username":
                alternatives = [
                    (By.CSS_SELECTOR, "input[type='text']"),
                    (By.CSS_SELECTOR, "input[type='email']"),
                    (By.NAME, "username"),
                    (By.XPATH, "//input[@type='text' or @type='email']")
                ]
            elif value == "pwd" or value == "password":
                alternatives = [
                    (By.CSS_SELECTOR, "input[type='password']"),
                    (By.NAME, "password"),
                    (By.XPATH, "//input[@type='password']")
                ]
            elif value == "loginBtn":
                alternatives = [
                    (By.CSS_SELECTOR, "button[type='submit']"),
                    (By.CSS_SELECTOR, "input[type='submit']"),
                    (By.XPATH, "//button[contains(text(), '登录') or contains(text(), 'Login')]"),
                    (By.XPATH, "//input[@value='登录' or @value='Login']")
                ]
            
            for alt_by, alt_value in alternatives:
                try:
                    element = WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((alt_by, alt_value))
                    )
                    if element.is_displayed():
                        logger.info(f"使用替代选择器找到元素: {alt_by}='{alt_value}'")
                        return element
                except TimeoutException:
                    continue
        
        # 方法4: 最后尝试，滚动页面查找
        try:
            for scroll_y in [0, 500, 1000]:
                self.driver.execute_script(f"window.scrollTo(0, {scroll_y});")
                time.sleep(0.5)
                try:
                    element = self.driver.find_element(by, value)
                    if element.is_displayed():
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                        return element
                except NoSuchElementException:
                    continue
        except Exception:
            pass
        
        raise TimeoutException(f"无法找到元素: {by}='{value}'")

    def __login(self):
        logger.info(f"正在访问登录页面: {self.url}")
        self.driver.get(self.url)
        
        # 等待页面加载
        self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
        time.sleep(2)
        
        # 应用Python 3.12修复
        self._apply_python312_fix()
        
        try:
            # 检查是否已经登录
            logger.info(f"检查是否已经登录，查找元素: {self.element_id}")
            self.driver.find_element(By.ID, self.element_id)
            logger.info("已经登录，无需重新登录")
            return
        except NoSuchElementException:
            logger.info("未登录，开始登录流程")
            
            try:
                # 查找并填写用户名
                logger.info("查找用户名输入框...")
                user_elem = self._smart_find_element(By.ID, "username")
                user_elem.clear()
                user_elem.send_keys(config.USER)
                
                # 查找并填写密码
                logger.info("查找密码输入框...")
                pwd_elem = self._smart_find_element(By.ID, "pwd")
                pwd_elem.clear()
                pwd_elem.send_keys(config.PASSWORD)
                
                # 查找并点击登录按钮
                logger.info("查找登录按钮...")
                login_btn = self._smart_find_element(By.ID, "loginBtn")
                
                # 点击登录按钮
                try:
                    login_btn.click()
                except Exception:
                    # 如果普通点击失败，使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", login_btn)
                
                logger.info("登录按钮点击成功")
                time.sleep(3)  # 简单等待登录处理

            except Exception as e:
                logger.error(f"登录过程失败: {e}")
                raise

            # 验证登录结果
            logger.info("验证登录结果...")
            self.driver.get(self.url)
            self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
            time.sleep(2)

            # 再次应用修复
            self._apply_python312_fix()

            try:
                # 验证登录是否成功
                self.driver.find_element(By.ID, self.element_id)
                logger.info("登录成功！")
            except NoSuchElementException:
                # 最后尝试智能查找
                try:
                    self._smart_find_element(By.ID, self.element_id, timeout=15)
                    logger.info("登录成功！")
                except:
                    logger.error("登录失败，请检查用户名和密码是否正确")
                    exit()

    def refresh(self):
        self.__login()

    def get_cookie(self):
        cookie_dict = {}
        dict_list = self.driver.get_cookies()
        for i in dict_list:
            name = i.get("name")
            value = i.get("value")
            if name and value:
                cookie_dict[name] = value

        self.driver.quit()
        return cookie_dict
