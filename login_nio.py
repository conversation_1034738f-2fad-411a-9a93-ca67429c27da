#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIO登录类 - 合并了改进功能
解决常见的登录和Cookie获取问题
"""

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from fake_useragent import UserAgent
import time
import requests

import config
from log_config import logger


class LoginNio:
    def __init__(self, url: str, element: str):
        if len(config.USER) == 0 or len(config.PASSWORD) == 0:
            logger.error(f"请先补全 {url} 页面的登录信息，至 config.py 文件!")
            raise ValueError("登录信息不完整")

        self.url = url
        self.element_id = element
        self.driver = None
        self.cookies = None

        self._setup_driver()
        self._login()

    def _setup_driver(self):
        """设置Chrome驱动"""
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument(f"--user-agent={UserAgent().chrome}")

        # 基础稳定性选项
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--start-maximized')

        # 禁用自动化检测
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.page_load_strategy = 'eager'

        self.driver = webdriver.Chrome(options=chrome_options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        self.driver.implicitly_wait(10)
        self.wait = WebDriverWait(self.driver, 30)

    def _find_element_smart(self, selectors, timeout=10):
        """智能查找元素，支持多种选择器"""
        for selector_type, selector_value in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((selector_type, selector_value))
                )
                if element.is_displayed():
                    logger.debug(f"找到元素: {selector_type}='{selector_value}'")
                    return element
            except TimeoutException:
                continue

        raise TimeoutException(f"无法找到任何匹配的元素")

    def _login(self):
        """执行登录流程"""
        logger.info(f"正在访问登录页面: {self.url}")
        self.driver.get(self.url)

        # 等待页面加载
        self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
        time.sleep(3)

        try:
            # 检查是否已经登录
            logger.info(f"检查是否已经登录，查找元素: {self.element_id}")
            self.driver.find_element(By.ID, self.element_id)
            logger.info("已经登录，无需重新登录")
            self._store_cookies()
            return
        except NoSuchElementException:
            logger.info("未登录，开始登录流程")

        # 执行登录
        self._perform_login()

        # 验证登录结果
        self._verify_login()

        # 存储cookies
        self._store_cookies()

    def _perform_login(self):
        """执行实际的登录操作"""
        try:
            # 查找用户名输入框
            logger.info("查找用户名输入框...")
            username_selectors = [
                (By.ID, "username"),
                (By.CSS_SELECTOR, "input[type='text']"),
                (By.CSS_SELECTOR, "input[type='email']"),
                (By.NAME, "username"),
                (By.XPATH, "//input[@type='text' or @type='email']")
            ]
            user_elem = self._find_element_smart(username_selectors)
            user_elem.clear()
            user_elem.send_keys(config.USER)
            logger.info(f"已输入用户名: {config.USER}")

            # 查找密码输入框
            logger.info("查找密码输入框...")
            password_selectors = [
                (By.ID, "pwd"),
                (By.ID, "password"),
                (By.CSS_SELECTOR, "input[type='password']"),
                (By.NAME, "password"),
                (By.XPATH, "//input[@type='password']")
            ]
            pwd_elem = self._find_element_smart(password_selectors)
            pwd_elem.clear()
            pwd_elem.send_keys(config.PASSWORD)
            logger.info("已输入密码")

            # 查找登录按钮
            logger.info("查找登录按钮...")
            login_selectors = [
                (By.ID, "loginBtn"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.CSS_SELECTOR, "input[type='submit']"),
                (By.XPATH, "//button[contains(text(), '登录') or contains(text(), 'Login')]"),
                (By.XPATH, "//input[@value='登录' or @value='Login']")
            ]
            login_btn = self._find_element_smart(login_selectors)

            # 点击登录按钮
            try:
                login_btn.click()
                logger.info("登录按钮点击成功")
            except Exception:
                # 如果普通点击失败，使用JavaScript点击
                self.driver.execute_script("arguments[0].click();", login_btn)
                logger.info("使用JavaScript点击登录按钮")

            logger.info("等待登录完成...")
            time.sleep(5)

        except Exception as e:
            logger.error(f"登录过程失败: {e}")
            raise

    def _verify_login(self):
        """验证登录结果"""
        logger.info("验证登录结果...")

        # 重新访问目标页面
        self.driver.get(self.url)
        self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
        time.sleep(3)

        try:
            # 验证登录是否成功
            self.driver.find_element(By.ID, self.element_id)
            logger.info("登录验证成功！")
        except NoSuchElementException:
            # 尝试其他可能的成功标识
            success_indicators = [
                (By.ID, self.element_id),
                (By.CSS_SELECTOR, ".main-content"),
                (By.CSS_SELECTOR, ".dashboard"),
                (By.CSS_SELECTOR, "[class*='container']"),
                (By.XPATH, "//div[contains(@class, 'main') or contains(@class, 'content')]")
            ]

            found = False
            for selector_type, selector_value in success_indicators:
                try:
                    self.driver.find_element(selector_type, selector_value)
                    logger.info(f"通过备用指示器验证登录成功: {selector_type}='{selector_value}'")
                    found = True
                    break
                except NoSuchElementException:
                    continue

            if not found:
                logger.error("登录验证失败，请检查用户名和密码是否正确")
                raise Exception("登录失败")

    def _store_cookies(self):
        """存储cookies"""
        self.cookies = {}
        dict_list = self.driver.get_cookies()
        for cookie in dict_list:
            name = cookie.get("name")
            value = cookie.get("value")
            if name and value:
                self.cookies[name] = value

        logger.info(f"已存储 {len(self.cookies)} 个Cookie")

    def test_api_access(self, api_url="https://phecda.nioint.com/aip-prod/vehicle/envchange/deploy/list"):
        """测试API访问是否正常"""
        if not self.cookies:
            logger.error("没有可用的Cookie")
            return False

        headers = {
            "Cookie": "; ".join([f"{k}={v}" for k, v in self.cookies.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Content-Type": "application/json"
        }

        try:
            logger.info(f"测试API访问: {api_url}")
            # 根据API测试结果，正确的参数名是 deploy_type
            request_data = {"deploy_type": "patch", "page": 1, "page_size": 20}
            response = requests.post(api_url, headers=headers, json=request_data, timeout=10)

            logger.info(f"API响应状态码: {response.status_code}")
            logger.debug(f"API响应头: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    # 先检查响应内容
                    response_text = response.text
                    logger.debug(f"API原始响应: {response_text[:200]}...")

                    if not response_text or response_text.strip() == "":
                        logger.warning("API返回空响应")
                        return False

                    data = response.json()
                    logger.debug(f"API解析后数据类型: {type(data)}")

                    if data is None:
                        logger.warning("API返回null数据")
                        return False

                    if isinstance(data, dict):
                        logger.debug(f"API响应数据键: {list(data.keys())}")
                        if 'deploy_list' in data:
                            deploy_list = data['deploy_list']
                            if deploy_list is not None:
                                logger.info(f"API访问成功，获取到 {len(deploy_list)} 条部署记录")
                                return True
                            else:
                                logger.warning("deploy_list为null")
                                return False
                        else:
                            logger.warning(f"API返回数据中没有deploy_list字段: {str(data)[:100]}...")
                            return False
                    else:
                        logger.warning(f"API返回数据格式异常，类型: {type(data)}, 内容: {str(data)[:100]}...")
                        return False

                except Exception as e:
                    logger.error(f"解析API响应失败: {e}")
                    logger.debug(f"原始响应内容: {response.text[:500]}...")
                    return False
            else:
                logger.error(f"API访问失败，状态码: {response.status_code}")
                logger.debug(f"错误响应内容: {response.text[:200]}...")
                return False

        except Exception as e:
            logger.error(f"API访问异常: {e}")
            return False

    def get_cookie(self):
        """获取cookies并关闭浏览器"""
        if self.cookies is None:
            logger.error("没有可用的Cookie")
            return {}

        # 测试API访问
        if self.test_api_access():
            logger.info("Cookie验证成功")
        else:
            logger.warning("Cookie可能无效，但仍然返回")

        # 关闭浏览器
        if self.driver:
            self.driver.quit()

        return self.cookies

    def refresh(self):
        """刷新登录"""
        if self.driver:
            self.driver.quit()
        self._setup_driver()
        self._login()

    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except:
                pass
