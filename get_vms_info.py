import requests
from typing import Optional

from log_config import logger
import json

class GetVMSInfo:
    def __init__(self, cookie: dict):
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json",
            "X-Fp-App-Id": "101686",
            "X-Fp-Domain": "vms-api-alps-stg.nioint.com",
        }

    def get_vehicle_detail(self, vin: str) -> Optional[dict]:
        self.method = "POST"
        self.url = "https://fp-stg.nioint.com/graphql/vehicleDetail"
        payload_dict = { "query": '''
            query vehicleProfileSet {
                vehicleDetail (search_id: "''' + vin + '''", search_type: "vin") {
                    rawStatusCode
                    successful
                    message
                    data {
                        static {
                            version_info {
                                pkg_ver {
                                    package_part_no
                                    package_global_version
                                    new_package_part_no
                                    new_package_global_version
                                }
                                ecu_versions {
                                    ecu
                                    software_pn
                                    hardware_pn
                                }
                            }
                        }
                    }
                }
            }
            '''
        }
        self.payload = json.dumps(payload_dict)
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            vehicle_detail = response_dict.get("data", {}).get("vehicleDetail", {})
            if vehicle_detail and vehicle_detail.get("message") == "success" and vehicle_detail.get("rawStatusCode") == 200:
                version_info = vehicle_detail.get("data", {}).get("static", {}).get("version_info")
                if version_info and len(version_info.get("ecu_versions", [])) > 0:
                    return version_info

        logger.error(f"Failed to get vehicle detail for vin {vin}")
        logger.warning(response.text)
        return None
