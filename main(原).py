#!/usr/bin/python3
import datetime
import json
import os
import sched
import time
from typing import Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from functools import lru_cache
import threading

import config
import login_nio
import get_phoenix_info
import get_sam_info
import get_fota_info
import get_tvas_info
import parse_spreadsheet
import vehicle_sw_version_table
import robot_request
from log_config import logger

# 导入系统配置
try:
    import system_config
    SYSTEM_OPTIMIZED = True
    logger.info("已加载系统优化配置")
except ImportError:
    SYSTEM_OPTIMIZED = False
    logger.warning("未找到系统配置文件，使用默认配置")

# 全局缓存变量 - 用于存储不同系统的查询结果
_phoenix_cache = {}  # Phoenix系统缓存
_sam_cache = {}      # SAM系统缓存
_cache_lock = threading.Lock()  # 缓存操作的线程锁

# 性能监控变量 - 记录缓存命中、处理时间等统计信息
_performance_stats = {
    'cache_hits': 0,
    'cache_misses': 0,
    'total_requests': 0,
    'processing_times': []
}

def cache_result(cache_dict, key_func=None):
    """
    缓存装饰器 - 用于缓存函数调用结果，减少重复查询
    :param cache_dict: 存储缓存的字典
    :param key_func: 生成缓存键的函数，默认为使用参数组合
    :return: 装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 更新总请求数
            _performance_stats['total_requests'] += 1

            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = str(args) + str(sorted(kwargs.items()))

            # 检查缓存命中
            with _cache_lock:
                if cache_key in cache_dict:
                    _performance_stats['cache_hits'] += 1
                    logger.debug(f"缓存命中 {func.__name__}: {cache_key}")
                    return cache_dict[cache_key]

            # 缓存未命中，执行函数并缓存结果
            _performance_stats['cache_misses'] += 1
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()

            with _cache_lock:
                cache_dict[cache_key] = result
                _performance_stats['processing_times'].append(end_time - start_time)
                logger.debug(f"缓存存储 {func.__name__}: {cache_key}, 耗时 {end_time - start_time:.3f}s")

            return result
        return wrapper
    return decorator

def print_performance_stats():
    """打印性能统计信息，包括缓存命中率和处理时间分布"""
    total_requests = _performance_stats['total_requests']
    cache_hits = _performance_stats['cache_hits']
    cache_misses = _performance_stats['cache_misses']
    processing_times = _performance_stats['processing_times']

    if total_requests > 0:
        hit_rate = (cache_hits / total_requests) * 100 if total_requests > 0 else 0
        logger.info(f"=== 性能统计 ===")
        logger.info(f"总请求数: {total_requests}")
        logger.info(f"缓存命中: {cache_hits}")
        logger.info(f"缓存未命中: {cache_misses}")
        logger.info(f"缓存命中率: {hit_rate:.2f}%")

        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            logger.info(f"平均处理时间: {avg_time:.3f}s")
            logger.info(f"最大处理时间: {max_time:.3f}s")
            logger.info(f"最小处理时间: {min_time:.3f}s")
        logger.info(f"===============")


def is_nt25_vehicle(vehicle_model: str) -> bool:
    """
    判断是否为NT2.5车型（仅特定车型的G1.F版本）
    :param vehicle_model: 车型信息
    :return: 是否为NT2.5车型
    """
    if not vehicle_model:
        return False

    # 仅这些车型的G1.F版本属于NT2.5
    nt25_vehicle_types = ["ET5", "ET7", "Libra", "Orion", "Sirius"]

    # 检查是否包含G1.F且车型在NT2.5列表中
    return "G1.F" in vehicle_model and any(vt in vehicle_model for vt in nt25_vehicle_types)


def get_target_version_for_vehicle(vehicle_model: str) -> str:
    """
    根据车型选择对应的目标版本
    :param vehicle_model: 车型信息
    :return: 目标版本号
    """
    if not vehicle_model:
        return config.NT2_VERSION

    # NT2.5车型使用专用版本
    if is_nt25_vehicle(vehicle_model):
        return config.NT25_VERSION

    # 默认返回NT2版本
    return config.NT2_VERSION


'''
* NT2车型相关处理
'''

# 缓存Phoenix系统的版本列表查询
@cache_result(_phoenix_cache, lambda vehicle_model: f"vehicle_release_{vehicle_model}")
def get_cached_vehicle_release(vehicle_model: str):
    """缓存的车型版本列表查询"""
    return phoenix.get_vehicle_release(vehicle_model)

# 缓存Phoenix系统的软件包列表查询
@cache_result(_phoenix_cache, lambda release_plan_id: f"package_list_{release_plan_id}")
def get_cached_package_list(release_plan_id: str):
    """缓存的软件包列表查询"""
    return phoenix.get_package_list(release_plan_id)

# 缓存Phoenix系统的软件包详情查询
@cache_result(_phoenix_cache, lambda package_id: f"package_detail_{package_id}")
def get_cached_package_detail(package_id: str):
    """缓存的软件包详情查询"""
    return phoenix.get_package_detail(package_id)

# 缓存SAM系统的VFR列表查询
@cache_result(_sam_cache, lambda brand: f"vfr_list_{brand}")
def get_cached_nt3_vfr_list(brand: str):
    """缓存的NT3 VFR列表查询"""
    return sam.get_nt3_vfr_list(brand)


def get_cache_size(cache_type: str, default: int) -> int:
    """
    获取缓存大小，优先使用系统配置
    :param cache_type: 缓存类型
    :param default: 默认大小
    :return: 缓存大小
    """
    return system_config.get_cache_size(cache_type) if SYSTEM_OPTIMIZED else default

# 缓存车型版本匹配结果 - 适配16GB内存，增加缓存大小
@lru_cache(maxsize=get_cache_size('vehicle_model', 256))
def get_target_version_for_vehicle_cached(vehicle_model: str) -> str:
    """缓存的车型目标版本获取"""
    return get_target_version_for_vehicle(vehicle_model)

# 缓存版本比较结果 - 版本比较频繁，增加缓存
@lru_cache(maxsize=get_cache_size('version_compare', 1024))
def compare_versions_cached(version1: str, version2: str) -> bool:
    """缓存的版本比较"""
    return compare_versions(version1, version2)

# 缓存车型平台判断结果 - 车型种类有限，适中缓存
@lru_cache(maxsize=get_cache_size('nt25_vehicle', 128))
def is_nt25_vehicle_cached(vehicle_model: str) -> bool:
    """缓存的NT2.5车型判断"""
    return is_nt25_vehicle(vehicle_model)

# 缓存PN有效性检查 - PN数量较多，增加缓存
@lru_cache(maxsize=get_cache_size('pn_validation', 2048))
def is_valid_pn_cached(pn: str) -> bool:
    """缓存的PN有效性检查"""
    return is_valid_pn(pn)

# 缓存调试包检查 - 调试包检查频繁，增加缓存
@lru_cache(maxsize=get_cache_size('debug_package', 1024))
def is_debug_package_cached(pn: str) -> bool:
    """缓存的调试包检查"""
    return is_debug_package(pn)

# 缓存乱码检查 - 乱码检查频繁，增加缓存
@lru_cache(maxsize=get_cache_size('messy_code', 2048))
def is_messy_code_cached(pn: str) -> bool:
    """缓存的乱码检查"""
    return is_messy_code(pn)


def get_release_scope(sw_version: str) -> Optional[str]:
    """
    获取软件版本的发布范围
    :param sw_version: 软件版本信息
    :return: 发布范围字符串或None
    """
    if not sw_version:
        return None
        
    sw_parts = sw_version.split()
    if len(sw_parts) < 2:
        return None
        
    sw_pn = sw_parts[0]
    sw_revision = sw_parts[-1]

    revision_list = phoenix.get_revision_list(sw_pn)
    if not revision_list:
        return None

    for revision_dict in revision_list:
        if revision_dict.get("swRevision") == sw_revision:
            return revision_dict.get("releaseScope")
    return None


def get_hardware_pn_list(hw_list: list, changed_hw_revs: list) -> list:
    """
    提取硬件零件号列表，包括基础列表和变更列表
    :param hw_list: 基础硬件列表
    :param changed_hw_revs: 硬件变更列表
    :return: 合并后的硬件零件号列表
    """
    res_list = []
    # 处理基础硬件列表
    for hw_dict in hw_list:
        pn = hw_dict.get("pn")
        revisions = hw_dict.get("revisions", [])
        for revision in revisions:
            res_list.append(f"{pn} {revision.get('name')}")

    # 处理变更硬件列表（仅添加+前缀的项目）
    for changed_hw_rev in changed_hw_revs:
        if changed_hw_rev.startswith("+"):
            res_list.append(changed_hw_rev[1:])  # 去除+前缀

    return res_list


def parse_package_detail(package_id: str):
    """
    解析软件包详情，构建ECU与软硬件的映射关系
    :param package_id: 软件包ID
    :return: ECU字典，格式{ecu_name: [(software_pn, [hardware_pn_list]), ...]}
    """
    package_detail_list = get_cached_package_detail(package_id)
    if not package_detail_list:
        return None

    ecu_dict = {}
    for package_detail in package_detail_list:
        sub_category = str(package_detail.get("subCategory", "")).lower()
        # 过滤校准相关的子类别（除100_calb外）
        if "calib" in sub_category or "cal" in sub_category:
            if "100_calb" not in sub_category:
                continue

        ecu_name = package_detail.get("ecuMark")
        software_pn = f"{package_detail.get('swPn')} {package_detail.get('swRevision')}"
        changed_hw_revs = package_detail.get("changedHwRevs", [])
        hw_list = package_detail.get("hwList", [])
        hardware_pn_list = get_hardware_pn_list(hw_list, changed_hw_revs)

        ecu_dict.setdefault(ecu_name, []).append((software_pn, hardware_pn_list))

    # 处理包含特殊分隔符的ECU名称（拆分并合并）
    need_to_add = []
    need_to_delete = []
    for key in ecu_dict:
        if any(sep in key for sep in ["/", "\\", "+"]):
            split_keys = key.replace("\\", "/").replace("+", "/").split("/")
            need_to_delete.append(key)
            for new_key in split_keys:
                need_to_add.append((new_key, key))

    # 执行拆分后的键值合并
    for new_key, old_key in need_to_add:
        if new_key in ecu_dict:
            ecu_dict[new_key] += ecu_dict[old_key]
        else:
            ecu_dict[new_key] = ecu_dict[old_key]
    for key in need_to_delete:
        del ecu_dict[key]

    return ecu_dict


def get_ecu_version_by_did(vin: str) -> Optional[dict]:
    """
    从标识报告(DID)中获取ECU版本信息
    :param vin: 车辆识别码
    :return: ECU版本信息字典或None
    """
    did_sheet_dir = os.path.join(dir_path, "did_sheet")
    # 查找包含VIN的DID文件
    for file in os.listdir(did_sheet_dir):
        if vin in file:
            path = os.path.join(did_sheet_dir, file)
            logger.debug(f"DID文件路径: {path}")
            did = parse_spreadsheet.DID(path)
            return did.get_ecu_version()

    logger.warning(f"未找到{vin}的DID文件")
    return None


def get_date_from_timestamp(timestamp):
    """
    将时间戳转换为日期时间字符串
    :param timestamp: 时间戳（10位或13位）
    :return: 格式化的日期时间字符串
    """
    if not timestamp:
        return "--"
    # 处理13位时间戳（取前10位）
    timestamp = int(str(timestamp)[0:10])
    dt = datetime.datetime.fromtimestamp(timestamp)
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def get_major_version(vehicle: str, engineer_version: str, bl_version: str):
    """
    获取主要版本信息（NT2车型）
    :param vehicle: 车型
    :param engineer_version: 工程版本
    :param bl_version: BL版本
    :return: (release_plan_id, major_rev)元组
    """
    sw_release_list = phoenix.get_vehicle_release(vehicle)
    if not sw_release_list:
        return None, None

    release_plan_id = None
    matched_engineer = []
    for sw_dict in sw_release_list:
        tmp_engineer = sw_dict.get("swPackageEngineerVersion")
        tmp_major = sw_dict.get("majorRev")
        
        if tmp_engineer == engineer_version:
            release_plan_id = sw_dict.get("id")
            if tmp_major == bl_version:
                return release_plan_id, tmp_major
            matched_engineer.append((release_plan_id, tmp_major))

    # 如果只有一个匹配的工程版本，直接返回
    return matched_engineer[0] if len(matched_engineer) == 1 else (release_plan_id, None)


def get_major_version_by_bl_version(vehicle: str, bl_version: str):
    """
    通过BL版本获取主要版本信息（NT2车型）
    :param vehicle: 车型
    :param bl_version: BL版本
    :return: (release_plan_id, major_rev)元组
    """
    # 处理特殊车型版本
    substrings = [".E", ".UAE", "RHD"]
    if any(sub in vehicle for sub in substrings):
        vehicle_model = vehicle
    else:
        vehicle_base = vehicle.split()[0]
        ga_versions = config.GA_VERSION.get(vehicle_base)
        vehicle_model = vehicle if ga_versions and vehicle in ga_versions else vehicle_base

    logger.debug(f"使用车型: {vehicle_model}")
    sw_release_list = get_cached_vehicle_release(vehicle_model)

    if not sw_release_list:
        return None, None

    # 调试日志：显示所有版本列表
    logger.info(f"Phoenix系统 {vehicle_model} 版本列表:")
    for i, sw_dict in enumerate(sw_release_list):
        logger.info(f"  {i+1}. 车型: '{sw_dict.get('vehicle')}', 版本: '{sw_dict.get('majorRev')}', ID: {sw_dict.get('id')}")

    # 查找精确匹配的版本
    for sw_dict in sw_release_list:
        if sw_dict.get("majorRev") == bl_version:
            logger.info(f"找到匹配版本: {bl_version}, ID: {sw_dict.get('id')}")
            return sw_dict.get("id"), sw_dict.get("majorRev")

    # 未找到时使用最新版本
    latest = sw_release_list[0]
    logger.warning(f"未找到{bl_version}，使用最新版本: {latest.get('majorRev')}")
    return latest.get("id"), latest.get("majorRev")


def get_hardware_compatibility_dict(release_plan_id) -> Optional[dict]:
    """
    获取硬件兼容性字典（NT2车型）
    :param release_plan_id: 发布计划ID
    :return: 硬件兼容性字典或None
    """
    if not release_plan_id:
        return None

    package_list = get_cached_package_list(release_plan_id)
    if not package_list:
        return None

    # 使用第一个软件包
    package_id = package_list[0].get("id")
    logger.debug(f"使用软件包: {package_list[0].get('name')}, ID: {package_id}")

    return parse_package_detail(package_id) if package_id else None


def update_summary_table_record_dict(fields: dict, record_id: str):
    """
    更新汇总表记录字典
    :param fields: 要更新的字段
    :param record_id: 记录ID
    """
    global summary_table_record_dict

    if not record_id or not record_id.strip():
        logger.warning(f"无效record_id: '{record_id}'，跳过更新")
        return

    if record_id in summary_table_record_dict:
        summary_table_record_dict[record_id]["fields"].update(fields)
    else:
        summary_table_record_dict[record_id] = {"fields": fields, "record_id": record_id}


def add_detail_table_records(records: list):
    """
    添加详细表记录
    :param records: 记录列表
    """
    global detail_table_records
    for record in records:
        detail_table_records.append({"fields": record})


def compare_versions(version1, version2):
    """
    比较两个版本号（BL版本）
    :param version1: 版本1
    :param version2: 版本2
    :return: version1 >= version2 则返回True
    """
    # 提取BL后的数字部分
    v1_num = version1.split("BL")[-1].split('.')
    v2_num = version2.split("BL")[-1].split('.')
    
    # 按段比较
    for i in range(3):
        v1 = int(v1_num[i]) if i < len(v1_num) else 0
        v2 = int(v2_num[i]) if i < len(v2_num) else 0
        if v1 > v2:
            return True
        if v1 < v2:
            return False
    return True


def is_messy_code(pn: str):
    """
    检查零件号是否存在乱码
    :param pn: 零件号
    :return: 是否为乱码
    """
    if not pn:
        return False

    pn_list = pn.strip().split()
    # 零件号应为两部分且均为字母数字组合
    return len(pn_list) != 2 or not all(x.isalnum() for x in pn_list)


def is_valid_pn(pn: str):
    """
    检查零件号是否有效
    :param pn: 零件号
    :return: 是否有效
    """
    return bool(pn) and not is_messy_code(pn)


def is_debug_package(pn: str):
    """
    检查是否为调试包
    :param pn: 零件号
    :return: 是否为调试包
    """
    if not pn:
        return False
    return pn.startswith(("D", "P4")) or pn.endswith("ZZ")


def process_single_vehicle(vin: str, vin_values: dict) -> tuple:
    """
    处理单个车辆的信息，返回处理结果
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :return: (vin, 汇总记录, 详细记录, 原始ECU记录)
    """
    try:
        logger.info(f"开始处理车辆: {vin}")

        # 本地记录存储，避免全局变量冲突
        local_summary = {}
        local_detail = []
        local_raw_ecu = []

        # 保存并替换全局变量
        global summary_table_record_dict, detail_table_records
        old_summary = summary_table_record_dict.copy() if 'summary_table_record_dict' in globals() else {}
        old_detail = detail_table_records.copy() if 'detail_table_records' in globals() else []
        
        summary_table_record_dict = local_summary
        detail_table_records = local_detail

        try:
            # 获取车型和平台信息
            vehicle_model, vehicle_platform = get_vehicle_model_platform(vin, vin_values)
            logger.info(f"VIN {vin}: 车型='{vehicle_model}', 平台={vehicle_platform}")

            if vehicle_platform:
                # 根据平台类型处理
                if vehicle_platform == "NT2.5":
                    raw_data = check_ecu_version_for_one_vehicle(vin, vin_values, vehicle_platform)
                    if raw_data:
                        local_raw_ecu.extend(raw_data)
                elif "NT2" in vehicle_platform:
                    raw_data = check_ecu_version_for_one_vehicle(vin, vin_values, vehicle_platform)
                    if raw_data:
                        local_raw_ecu.extend(raw_data)
                elif "NT3" in vehicle_platform:
                    if vehicle_model and vehicle_model.lower() in ["dom", "blanc", "rosa"]:
                        raw_data = check_nt3_ecu_version_for_one_vehicle("alps", vin, vin_values, vehicle_model)
                        if raw_data:
                            local_raw_ecu.extend(raw_data)
                    elif vehicle_model and vehicle_model.lower() in ["leo", "cetus"]:
                        if skip_leo:
                            return vin, None, [], []
                        raw_data = check_nt3_ecu_version_for_one_vehicle("nio", vin, vin_values, vehicle_model)
                        if raw_data:
                            local_raw_ecu.extend(raw_data)
                else:
                    logger.warning(f"未知平台: {vehicle_platform}")
                    return vin, None, [], []

            logger.info(f"完成处理车辆: {vin}, 原始ECU记录数: {len(local_raw_ecu)}")
            return vin, local_summary, local_detail, local_raw_ecu

        finally:
            # 恢复全局变量
            summary_table_record_dict = old_summary
            detail_table_records = old_detail

    except Exception as e:
        logger.error(f"处理车辆 {vin} 错误: {str(e)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return vin, None, [], []


def check_ecu_version_for_one_vehicle(vin: str, vin_values: dict, vehicle_platform: str = None):
    """
    检查单个NT2/NT2.5车辆的ECU版本兼容性
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :param vehicle_platform: 车辆平台
    :return: 原始ECU数据列表
    """
    # 获取ECU版本信息（优先DID文件，其次TVAS）
    ecu_version_info = None
    use_did = vin_values.get("use_did")
    if use_did:
        ecu_version_info = get_ecu_version_by_did(vin)

    # 本地无DID文件时从TVAS获取
    if not ecu_version_info:
        global get_tvas_info_request
        ecu_version_info = get_tvas_info_request.query_ecu_version(vin)
        if not ecu_version_info:
            logger.warning(f"无法获取{vin}的ECU版本信息")
            update_summary_table_record_dict({
                "VIN": vin,
                "软件包工程版本": "未知",
                "版本迭代号": "未知",
                "硬件是否都兼容": "不确定",
                "各ECU软件都对齐至整车版本": "不确定",
                "采样时间": None,
                "备注": "获取不到ECU的软硬件零件号"
            }, vin_values.get("record_id"))
            return []

    # 提取ECU信息
    sample_time = ecu_version_info.get("sample_time")
    sample_time_ms = int(sample_time) * 1000 if sample_time else None
    ecu_version_dict = ecu_version_info.get("ecu_version_map", {})
    ecu_version_list = list(ecu_version_dict.values())

    # 收集原始ECU数据
    vehicle_model = vin_values.get("vehicle_model", "")
    raw_ecu_data = collect_raw_ecu_data(
        vin, ecu_version_list, vehicle_model, vehicle_platform, sample_time_ms
    )

    return raw_ecu_data


def check_compatibility(hardware_compatibility_dict: dict, ecu_version_list: list, 
                       one_record_dict: dict, vin: str, vin_values: dict):
    """
    检查软硬件兼容性
    :param hardware_compatibility_dict: 硬件兼容性字典
    :param ecu_version_list: ECU版本列表
    :param one_record_dict: 单条记录字典
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    """
    if not hardware_compatibility_dict:
        logger.warning(f"无法获取{vin}的硬件兼容性信息")
        one_record_dict.update({
            "硬件是否都兼容": "不确定",
            "各ECU软件都对齐至整车版本": "不确定",
            "备注": "获取不到此版本的软硬件兼容表"
        })
        update_summary_table_record_dict(one_record_dict, vin_values.get("record_id"))
        return

    # 软硬件兼容性检查结果
    replace_hardware = []  # 需更换硬件列表
    upgrade_software = []  # 需升级软件列表

    for ecu_info in ecu_version_list:
        ecu_name = ecu_info.get("ecu")
        if ecu_name not in hardware_compatibility_dict:
            continue

        # 获取当前ECU的软硬件信息
        current_sw = ecu_info.get("software_pn", "").strip("\x00\x02")
        current_hw = ecu_info.get("hardware_pn", "").strip("\x00\x02")

        # 提取目标软硬件信息
        hw_set = set()
        sw_set = set()
        for sw_pn, hw_list in hardware_compatibility_dict[ecu_name]:
            hw_set.update(hw_list)
            sw_set.add(sw_pn)

        # Dom车型硬件过滤
        vehicle_model = vin_values.get("vehicle_model", "").lower()
        if vehicle_model == "dom":
            hw_set = {hw for hw in hw_set if hw and is_valid_pn(hw)}

        # 硬件信息检查
        hw_check = {
            "VIN": vin,
            "硬件不兼容的ECU": ecu_name,
            "当前硬件零件号": current_hw,
            "可更换的硬件": list(hw_set)
        }

        if not current_hw or is_messy_code(current_hw):
            hw_check["备注"] = "硬件零件号为空或乱码"
            replace_hardware.append(hw_check)
            continue

        # 软硬件匹配检查
        hw_matched = False
        sw_matched = False
        sw_check = None

        # 拆分当前软件零件号
        current_sw_parts = current_sw.split() if current_sw else []
        current_sw_num = current_sw_parts[0] if len(current_sw_parts) > 0 else ""
        current_sw_rev = current_sw_parts[-1] if len(current_sw_parts) > 1 else ""

        # 检查每个目标软件
        for sw_pn, hw_list in hardware_compatibility_dict[ecu_name]:
            target_sw_parts = sw_pn.split()
            if len(target_sw_parts) < 2:
                continue
                
            target_sw_num = target_sw_parts[0]
            target_sw_rev = target_sw_parts[-1]

            # 硬件匹配检查
            if current_hw in hw_list or not hw_list:
                hw_matched = True

                # 软件匹配检查
                if (is_valid_pn(current_sw) and 
                    (current_sw_num == target_sw_num or is_debug_package(current_sw))):
                    sw_matched = True

                    # 版本未对齐情况
                    if (not is_debug_package(current_sw) and 
                        current_sw_rev < target_sw_rev):
                        sw_check = {
                            "VIN": vin,
                            "软件未对齐的ECU": ecu_name,
                            "当前软件零件号": current_sw,
                            "目标软件零件号": [sw_pn],
                            "备注": "软件兼容，但版本未对齐"
                        }
                    break

        # 处理软件不匹配情况
        if hw_matched and not sw_matched:
            sw_check = {
                "VIN": vin,
                "软件未对齐的ECU": ecu_name,
                "当前软件零件号": current_sw,
                "目标软件零件号": list(sw_set)
            }
            if not current_sw:
                sw_check["备注"] = "软件零件号为空"
            elif is_messy_code(current_sw):
                sw_check["备注"] = "软件零件号乱码"
            else:
                sw_check["备注"] = "软件不兼容"

        if sw_check:
            upgrade_software.append(sw_check)

        # 处理硬件不匹配情况
        if not hw_matched:
            replace_hardware.append(hw_check)

    # 更新检查结果
    if replace_hardware:
        one_record_dict["硬件是否都兼容"] = "不兼容"
        one_record_dict["各ECU软件都对齐至整车版本"] = "未对齐"
        replace_hardware.sort(key=lambda x: x["硬件不兼容的ECU"])
        add_detail_table_records(replace_hardware)

    if upgrade_software:
        one_record_dict["各ECU软件都对齐至整车版本"] = "未对齐"
        upgrade_software.sort(key=lambda x: x["软件未对齐的ECU"])
        add_detail_table_records(upgrade_software)

    update_summary_table_record_dict(one_record_dict, vin_values.get("record_id"))


'''
* NT3车型相关处理
'''

def get_nt3_hardware_pn_list(hw_list: list, changed_hw_revs: list, ecu_name: str):
    """
    提取NT3车型的硬件零件号列表
    :param hw_list: 基础硬件列表
    :param changed_hw_revs: 硬件变更列表
    :param ecu_name: ECU名称
    :return: 硬件零件号列表
    """
    res_list = []
    # 处理基础硬件列表
    for hw_dict in hw_list:
        if hw_dict.get("ecu_name", "").lower() != ecu_name.lower():
            continue

        for hw_pn_dict in hw_dict.get("children", []):
            hw_pn = hw_pn_dict.get("label", "")
            for rev_dict in hw_pn_dict.get("children", []):
                res_list.append(f"{hw_pn} {rev_dict.get('label', '')}")

    # 处理变更硬件列表
    for changed_hw in changed_hw_revs:
        if changed_hw.startswith("+") and changed_hw.split()[0][1:].lower() == ecu_name.lower():
            hw_pn_rev = changed_hw.split(ecu_name)[-1].strip()
            if hw_pn_rev and hw_pn_rev not in res_list:
                res_list.append(hw_pn_rev)

    return res_list


def parse_nt3_package_detail(package_detail_list: list):
    """
    解析NT3车型的软件包详情
    :param package_detail_list: 软件包详情列表
    :return: ECU字典，格式{ecu_name: [(software_pn, [hardware_pn_list]), ...]}
    """
    if not package_detail_list:
        return None

    ecu_dict = {}
    for detail in package_detail_list:
        sub_category = str(detail.get("sub_category", "")).lower()
        # 过滤校准相关的子类别（除100_calb外）
        if "calib" in sub_category or "cal" in sub_category:
            if "100_calb" not in sub_category:
                continue

        # 构建软件零件号
        sw_pn = f"{detail.get('sw_pn')} {detail.get('current_revision')}"
        if not sw_pn:
            continue

        # 提取硬件信息
        hw_list = detail.get("hw_list", [])
        changed_hw = detail.get("changed_hw_list", [])
        original_ecu = detail.get("ecu_mark", "")
        
        # 处理包含+的ECU名称
        for ecu_name in original_ecu.split("+"):
            hw_pn_list = get_nt3_hardware_pn_list(hw_list, changed_hw, ecu_name)
            ecu_dict.setdefault(ecu_name, []).append((sw_pn, hw_pn_list))

    return ecu_dict


def get_package_detail(brand: str, package_name=None, vehicle_model=None):
    """
    获取NT3车型的软件包详情
    :param brand: 品牌
    :param package_name: 软件包名称
    :param vehicle_model: 车型
    :return: 软件包详情或None
    """
    # Dom车型调试：清除缓存确保获取最新数据
    is_dom = vehicle_model and vehicle_model.lower().startswith("dom")
    if is_dom:
        logger.info("Dom车型调试: 清除SAM缓存")
        cache_key = f"vfr_list_{brand}"
        if cache_key in _sam_cache:
            del _sam_cache[cache_key]

    # 获取VFR列表
    nt3_vfr_list = get_cached_nt3_vfr_list(brand)
    if not nt3_vfr_list:
        logger.warning(f"SAM系统未返回{brand}品牌的VFR列表")
        return None

    # 日志：显示相关版本信息
    logger.info(f"SAM系统 {vehicle_model} 版本列表:")
    relevant_vfrs = []
    for vfr in nt3_vfr_list:
        vfr_vehicle = vfr.get("vehicle_type", "")
        if vehicle_model and (vehicle_model.lower() == vfr_vehicle.lower() or 
                             vehicle_model.split()[0].lower() == vfr_vehicle.split()[0].lower()):
            relevant_vfrs.append(vfr)
    
    for i, vfr in enumerate(relevant_vfrs):
        logger.info(f"  {i+1}. 车型: '{vfr.get('vehicle_type')}', 版本: '{vfr.get('name')}', ID: {vfr.get('id')}")

    # 查找匹配的release_plan_id
    release_plan_id = None
    # 第一轮：完全匹配（车型+版本）
    if vehicle_model and package_name:
        for vfr in nt3_vfr_list:
            if (vehicle_model.lower() == vfr.get("vehicle_type", "").lower() and 
                package_name == vfr.get("name")):
                release_plan_id = vfr.get("id")
                logger.info(f"找到完全匹配: {vehicle_model} + {package_name}, ID: {release_plan_id}")
                break

    # 第二轮：基础车型匹配
    if not release_plan_id and vehicle_model:
        base_model = vehicle_model.split()[0].lower()
        for vfr in nt3_vfr_list:
            vfr_base = vfr.get("vehicle_type", "").split()[0].lower()
            if base_model == vfr_base and (not package_name or package_name == vfr.get("name")):
                release_plan_id = vfr.get("id")
                logger.info(f"找到基础车型匹配: {vehicle_model} -> {vfr.get('vehicle_type')}, ID: {release_plan_id}")
                break

    if not release_plan_id:
        logger.error(f"未找到匹配的VFR: brand={brand}, model={vehicle_model}, version={package_name}")
        return None

    # 获取软件配置列表
    sw_config_list = sam.get_nt3_vfr_sw_config_list(brand, release_plan_id)
    if not sw_config_list:
        logger.warning(f"未找到软件配置列表: {release_plan_id}")
        return "NO_SW_CONFIG"

    # 获取软件包详情
    sw_config_id = sw_config_list[0].get("id")
    base_version = sw_config_list[0].get("base_version") or sw_config_list[0].get("sw_name")
    
    if sw_config_id and base_version:
        return sam.get_package_detail(brand, sw_config_id, base_version)
    
    return None


def get_ecu_version_by_fota_web(vin: str, brand: str):
    """
    从FOTA系统获取ECU版本信息（NT3车型）
    :param vin: 车辆识别码
    :param brand: 品牌
    :return: ECU版本信息字典或None
    """
    global fota_onvo_stg, fota_nio_stg
    
    if brand.lower() in ["dom", "blanc", "rosa", "alps", "onvo"]:
        vehicle_info = fota_onvo_stg.query_vehicle_info(vin)
        if vehicle_info and vehicle_info.get("vid"):
            return fota_onvo_stg.query_ecu_version_by_fota(vehicle_info.get("vid"))
    else:
        if skip_leo:
            return None
        vehicle_info = fota_nio_stg.query_vehicle_info(vin)
        if vehicle_info and vehicle_info.get("vid"):
            return fota_nio_stg.query_ecu_version_by_fota(vehicle_info.get("vid"))

    return None


def get_complete_vehicle_model_from_config(simplified_model: str) -> str:
    """
    从配置中获取完整车型名称
    :param simplified_model: 简化车型名称
    :return: 完整车型名称
    """
    if not simplified_model:
        return simplified_model

    # 查找配置中的GA版本
    ga_versions = config.GA_VERSION.get(simplified_model)
    if ga_versions and ga_versions:
        return ga_versions[0]  # 返回第一个版本

    logger.warning(f"未找到{simple_model}的GA版本配置")
    return simplified_model


def check_nt3_ecu_version_for_one_vehicle(brand: str, vin: str, vin_values: dict, vehicle_model: str = None):
    """
    检查单个NT3车辆的ECU版本兼容性
    :param brand: 品牌
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :param vehicle_model: 车型
    :return: 原始ECU数据列表
    """
    logger.info(f"处理NT3车型: brand={brand}, model='{vehicle_model}', vin={vin}")
    is_dom = vehicle_model and vehicle_model.lower() == "dom"

    # 初始化变量
    ecu_version_list = None
    sample_time_ms = None
    package_global_version = None
    latest_engineer_version = None
    latest_bl_version = None
    original_vehicle_model = vehicle_model

    # 获取ECU版本信息（优先DID，其次FOTA）
    use_did = vin_values.get("use_did")
    if use_did:
        ecu_version_info = get_ecu_version_by_did(vin)
        if ecu_version_info:
            logger.info(f"从DID获取ECU信息: {vin}")
            ecu_version_list = list(ecu_version_info.get("ecu_version_map", {}).values())
            package_global_version = ecu_version_info.get("package_global_version")
            sample_time_ms = int(ecu_version_info.get("sample_time", 0)) * 1000

    # DID获取失败时从FOTA获取
    if not ecu_version_list:
        logger.info(f"从FOTA获取ECU信息: {vin}")
        ecu_version_info = get_ecu_version_by_fota_web(vin, brand)
        if ecu_version_info:
            ecu_version_list = list(ecu_version_info.get("ecu_version_map", {}).values())
            package_global_version = ecu_version_info.get("package_global_version")
            sample_time_ms = int(ecu_version_info.get("sample_time", 0)) * 1000

    # 从版本信息提取完整车型
    if package_global_version:
        # 解析版本信息
        latest_engineer_version = str(package_global_version.split('*')[0].split('_')[0])
        tmp_bl = str(package_global_version.split('*')[-1].split("BL")[-1])
        latest_bl_version = "BL" + ''.join([c for c in tmp_bl if c.isdigit() or c == '.'])
        
        # 提取完整车型
        detailed_model = ('.'.join(package_global_version.split('.')[0:3])).replace('.', ' ', 1)
        logger.info(f"从版本提取车型: '{detailed_model}'")

        # Dom车型特殊处理
        if is_dom and detailed_model:
            if detailed_model.split()[0].lower() != "dom":
                logger.warning(f"Dom车型ECU信息异常: {detailed_model}")
                # 使用配置中的默认车型
                vehicle_model = get_complete_vehicle_model_from_config(original_vehicle_model)
            else:
                vehicle_model = detailed_model
        elif detailed_model and ' G1.' in detailed_model:
            vehicle_model = detailed_model

    # 收集原始ECU数据
    vehicle_platform = vin_values.get("vehicle_platform", "NT3")
    raw_ecu_data = collect_raw_ecu_data(
        vin, ecu_version_list, vehicle_model, vehicle_platform, sample_time_ms
    )

    # 选择目标版本
    vehicle_base = original_vehicle_model.split()[0].lower() if original_vehicle_model else ""
    if brand == "alps":
        # ONVO品牌车型
        if vehicle_base == "dom":
            package_name = config.DOM_VERSION
        elif vehicle_base == "blanc":
            package_name = config.BLANC_VERSION
        elif vehicle_base == "rosa":
            package_name = config.ROSA_VERSION
        else:
            package_name = config.DOM_VERSION  # 兜底
    else:
        # NIO品牌车型
        if vehicle_base == "cetus":
            package_name = config.CETUS_VERSION
        elif vehicle_base == "leo":
            package_name = config.LEO_VERSION
        else:
            package_name = config.LEO_VERSION  # 兜底

    logger.info(f"{vehicle_model}使用版本: {package_name}")

    # 获取硬件兼容性信息
    hardware_compatibility_dict = None
    pkg_detail = get_package_detail(brand, package_name, vehicle_model)

    if pkg_detail == "NO_SW_CONFIG":
        # 找到VFR但无配置包
        update_summary_table_record_dict({
            "VIN": vin,
            "软件包工程版本": f"{vehicle_model} {package_name}",
            "版本迭代号": package_name,
            "硬件是否都兼容": "待确认",
            "各ECU软件都对齐至整车版本": "待确认",
            "采样时间": None,
            "备注": f"找到{brand} {vehicle_model}的VFR，但暂无软件配置包"
        }, vin_values.get("record_id"))
        return raw_ecu_data
    elif not pkg_detail:
        # 获取详情失败
        update_summary_table_record_dict({
            "VIN": vin,
            "软件包工程版本": "未知",
            "版本迭代号": "未知",
            "硬件是否都兼容": "不确定",
            "各ECU软件都对齐至整车版本": "不确定",
            "采样时间": None,
            "备注": f"无法获取{brand} {vehicle_model}的软件包详情"
        }, vin_values.get("record_id"))
        return raw_ecu_data

    # 解析硬件兼容性信息
    hardware_compatibility_dict = parse_nt3_package_detail(pkg_detail)

    # 检查ECU版本信息
    if not ecu_version_list:
        logger.error(f"无法获取{vin}的ECU信息")
        update_summary_table_record_dict({
            "VIN": vin,
            "软件包工程版本": "未知",
            "版本迭代号": "未知",
            "硬件是否都兼容": "不确定",
            "各ECU软件都对齐至整车版本": "不确定",
            "采样时间": None,
            "备注": f"获取不到ECU的软硬件零件号 (车型: {vehicle_model})"
        }, vin_values.get("record_id"))
        return raw_ecu_data

    # 构建记录字典
    if package_global_version:
        record_dict = {
            "VIN": vin,
            "软件包工程版本": latest_engineer_version,
            "版本迭代号": latest_bl_version,
            "目标迭代号": package_name,
            "硬件是否都兼容": "兼容",
            "各ECU软件都对齐至整车版本": "已对齐",
            "采样时间": sample_time_ms,
            "备注": None,
        }
    else:
        record_dict = {
            "VIN": vin,
            "软件包工程版本": "空值",
            "版本迭代号": "空值",
            "目标迭代号": package_name,
            "硬件是否都兼容": "兼容",
            "各ECU软件都对齐至整车版本": "已对齐",
            "采样时间": sample_time_ms,
            "备注": "F141为空，无法获取整车版本号",
        }

    # 执行兼容性检查
    vin_values["vehicle_model"] = vehicle_model  # 确保包含车型信息
    check_compatibility(hardware_compatibility_dict, ecu_version_list, record_dict, vin, vin_values)

    return raw_ecu_data


def get_vehicle_model_platform(vin: str, vin_values: dict):
    """
    获取车型和平台信息
    :param vin: 车辆识别码
    :param vin_values: 车辆相关参数
    :return: (vehicle_model, platform)元组
    """
    global get_tvas_info_request

    res_dict = get_tvas_info_request.query_by_vin(vin)
    if res_dict:
        vehicle_model = res_dict.get("vehicle_model")
        platform = res_dict.get("platform")

        # NT2.5车型特殊标记
        if platform and "NT2" in platform and is_nt25_vehicle(vehicle_model):
            platform = "NT2.5"

        update_summary_table_record_dict({
            "车型": vehicle_model,
            "车型平台": platform,
        }, vin_values.get("record_id"))
        return vehicle_model, platform
    else:
        logger.warning(f"无法获取{vin}的车型和平台信息")
        return None, None


def did_info_upload(vehicles_info: dict):
    """
    上传DID信息到FOTA系统
    :param vehicles_info: 车辆信息字典
    """
    global fota_onvo_stg, fota_nio_stg

    onvo_vids = []
    nio_vids = []
    for vin, values in vehicles_info.items():
        model = values.get("vehicle_model", "").lower()
        if model in ["dom", "blanc", "rosa"]:
            # ONVO品牌车辆
            info = fota_onvo_stg.query_vehicle_info(vin)
            if info and info.get("vid"):
                onvo_vids.append(info["vid"])
        elif model in ["leo", "cetus"] and not skip_leo:
            # NIO品牌车辆
            info = fota_nio_stg.query_vehicle_info(vin)
            if info and info.get("vid"):
                nio_vids.append(info["vid"])

    # 发送OTA命令
    if onvo_vids:
        if fota_onvo_stg.send_ota_cmd(onvo_vids):
            logger.debug(f"发送OTA命令到ONVO车辆: {onvo_vids}")
        else:
            logger.warning(f"ONVO车辆OTA命令发送失败")
    if nio_vids:
        if fota_nio_stg.send_ota_cmd(nio_vids):
            logger.debug(f"发送OTA命令到NIO车辆: {nio_vids}")
        else:
            logger.warning(f"NIO车辆OTA命令发送失败")

    if not onvo_vids and not nio_vids:
        logger.info("未找到可发送OTA命令的车辆")


def process_vehicles_concurrently(vehicle_info_dict: dict, max_workers: int = 8):
    """
    并发处理多辆车的信息
    :param vehicle_info_dict: 车辆信息字典
    :param max_workers: 最大并发数
    :return: (汇总记录, 详细记录, 原始ECU记录)
    """
    logger.info(f"并发处理 {len(vehicle_info_dict)} 辆车，最大并发数: {max_workers}")

    all_summary = []
    all_detail = []
    all_raw_ecu = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_vin = {
            executor.submit(process_single_vehicle, vin, vals): vin
            for vin, vals in vehicle_info_dict.items()
        }

        # 处理完成的任务
        completed = 0
        total = len(future_to_vin)
        for future in as_completed(future_to_vin):
            completed += 1
            vin = future_to_vin[future]
            
            try:
                _, summary, detail, raw_ecu = future.result()
                logger.info(f"进度: {completed}/{total} - 完成车辆 {vin}")

                if summary:
                    all_summary.extend(summary.values())
                if detail:
                    all_detail.extend(detail)
                if raw_ecu:
                    all_raw_ecu.extend(raw_ecu)

            except Exception as e:
                logger.error(f"处理车辆 {vin} 异常: {str(e)}")
                logger.error(f"异常堆栈: {traceback.format_exc()}")

    logger.info(f"并发处理完成: 汇总{len(all_summary)}条, 详细{len(all_detail)}条, 原始ECU{len(all_raw_ecu)}条")
    return all_summary, all_detail, all_raw_ecu


def ecu_version_checker():
    """ECU版本检查主函数"""
    if not config.DEBUG:
        # 非调试模式下仅在凌晨3点运行
        now = datetime.datetime.now()
        if now.hour != 3:
            logger.debug("仅在凌晨3点运行ECU版本检查")
            return

    # 初始化表格并获取车辆信息
    summary_table = vehicle_sw_version_table.Summary()
    vehicle_info = summary_table.get_vehicle_info_dict()
    did_info_upload(vehicle_info)  # 上传DID信息

    # 清理历史数据
    detail_table = vehicle_sw_version_table.VehicleDetail()
    all_record_ids = detail_table.get_all_record_ids(skip_leo)
    detail_table.batch_delete(all_record_ids)

    # 清空原始ECU数据表
    try:
        raw_ecu_table = vehicle_sw_version_table.RawEcuData()
        raw_ids = raw_ecu_table.get_all_record_ids()
        if raw_ids:
            logger.info(f"清空原始ECU表 {len(raw_ids)} 条记录")
            raw_ecu_table.batch_delete(raw_ids)
    except Exception as e:
        logger.error(f"清空原始ECU表失败: {e}")

    # 并发处理车辆信息
    start_time = time.time()
    # 确定最佳并发数
    if SYSTEM_OPTIMIZED:
        optimal_workers = system_config.get_optimal_workers(len(vehicle_info), 'io_intensive')
        rec = system_config.get_recommended_settings(len(vehicle_info))
        logger.info(f"系统优化配置 - 车辆数: {len(vehicle_info)}, 并发数: {optimal_workers}")
        logger.info(f"预计时间: {rec['estimated_time']:.1f}s, 内存: {rec['memory_usage_mb']}MB")
    else:
        # 默认配置：i7-7700 4核8线程
        optimal_workers = min(8, max(4, len(vehicle_info) // 10))
        logger.info(f"默认配置 - 车辆数: {len(vehicle_info)}, 并发数: {optimal_workers}")

    # 执行并发处理
    summaries, details, raw_ecus = process_vehicles_concurrently(vehicle_info, optimal_workers)
    logger.info(f"总处理时间: {time.time() - start_time:.2f}秒")

    # 批量更新数据库
    try:
        # 更新汇总表
        if summaries:
            valid_summaries = [r for r in summaries if r.get("record_id") and r["record_id"].strip()]
            if valid_summaries:
                logger.info(f"更新 {len(valid_summaries)} 条汇总记录")
                if not summary_table.batch_update(valid_summaries):
                    logger.warning("部分汇总记录更新失败")

        # 更新详细表
        if details:
            logger.info(f"创建 {len(details)} 条详细记录")
            formatted = [{"fields": r} if "fields" not in r else r for r in details]
            if not detail_table.batch_create(formatted):
                logger.warning("部分详细记录创建失败")

        # 插入原始ECU数据
        if raw_ecus:
            logger.info(f"插入 {len(raw_ecus)} 条原始ECU记录")
            formatted = [{"fields": r} if "fields" not in r else r for r in raw_ecus]
            raw_ecu_table = vehicle_sw_version_table.RawEcuData()
            
            if not raw_ecu_table.batch_create(formatted):
                logger.error("原始ECU批量插入失败，尝试逐条插入")
                success = 0
                for rec in formatted:
                    try:
                        if raw_ecu_table.create_record(rec.get("fields", rec)):
                            success += 1
                    except Exception as e:
                        logger.error(f"单条插入失败: {e}")
                logger.info(f"逐条插入完成: {success}/{len(formatted)}")

    except Exception as e:
        logger.error(f"数据库更新失败: {e}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")

    # 完成处理
    vehicle_count = len(vehicle_info)
    msg = f"共 {vehicle_count} 车的软硬件零件号同步完成!"
    logger.info(msg)
    print_performance_stats()  # 打印性能统计
    robot.set_payload("[软硬件零件号]运行正常", msg)
    robot.request()


def get_trigger_time():
    """获取程序触发时间（默认每天凌晨3点，支持立即运行）"""
    now = datetime.datetime.now()
    # 计算今天或明天的凌晨3点
    today_3am = datetime.datetime(now.year, now.month, now.day, 3, 0, 0)
    if now.hour >= 3:
        today_3am += datetime.timedelta(days=1)

    logger.info(f"程序默认将在 {today_3am.strftime('%Y-%m-%d %H:%M:%S')} 运行")
    
    # 用户确认是否立即运行
    print(f"是否立即运行程序？(y=立即/n=默认时间，默认n)")
    user_input = input().strip().lower()
    
    if user_input in ['y', 'yes']:
        # 立即运行
        logger.info("用户选择立即运行")
        return [now.timestamp()]
    else:
        # 默认时间运行
        return [today_3am.timestamp()]


def collect_raw_ecu_data(vin: str, ecu_version_list: list, vehicle_model: str, 
                        vehicle_platform: str, sample_time_ms: int):
    """
    收集原始ECU数据
    :param vin: 车辆识别码
    :param ecu_version_list: ECU版本列表
    :param vehicle_model: 车型
    :param vehicle_platform: 平台
    :param sample_time_ms: 采样时间戳
    :return: 原始ECU记录列表
    """
    raw_records = []
    for ecu_info in ecu_version_list:
        raw_records.append({
            "VIN": vin,
            "车型平台": vehicle_platform,
            "车型": vehicle_model,
            "ECU": [ecu_info.get("ecu", "")] if ecu_info.get("ecu") else [],
            "当前硬件零件号": ecu_info.get("hardware_pn", "").strip("\x00\x02"),
            "当前软件零件号": ecu_info.get("software_pn", "").strip("\x00\x02"),
            "最后更新时间": sample_time_ms
        })
    return raw_records


if __name__ == "__main__":
    import traceback  # 延迟导入，仅主程序需要
    dir_path = os.path.dirname(__file__)
    robot = robot_request.RebotRequest()

    # 全局变量初始化
    summary_table_record_dict = {}
    detail_table_records = []
    skip_leo = False  # 是否跳过Leo车型处理

    try:
        while True:
            # 登录各系统并获取客户端实例（封装重复登录逻辑）
            def login_system(url, element_id):
                """通用登录函数"""
                login = login_nio.LoginNio(url, element_id)
                return login.get_cookie()

            # 登录SAM系统
            sam_cookie = login_system("https://neep.nioint.com/#/artifact/release/vfr", "app")
            sam = get_sam_info.GetSamInfo(sam_cookie)

            # 登录Phoenix系统
            phoenix_cookie = login_system("https://phoenix.nioint.com/#/fr/cfr", "wm_div_id")
            phoenix = get_phoenix_info.GetPhoenixInfo(phoenix_cookie)

            # 登录TVAS系统
            tvas_cookie = login_system("https://tvas.nioint.com/tvas/vehicleResource", "container")
            get_tvas_info_request = get_tvas_info.GetTvasInfo(tvas_cookie)

            # 登录FOTA-ONVO系统
            fota_onvo_cookie = login_system("https://fota-web-onvo-stg.nioint.com/v2/#/fota/vehicles", "app")
            fota_onvo_cookie_str = "; ".join([f"{k}={v}" for k, v in fota_onvo_cookie.items()])
            fota_onvo_stg = get_fota_info.GetFotaInfo(fota_onvo_cookie_str, "https://fota-web-onvo-stg.nioint.com/")

            # 登录FOTA-NIO系统
            fota_nio_stg = get_fota_info.GetFotaInfo(config.FOTA_COOKIE, "https://fota-web-stg.nioint.com/")
            if not fota_nio_stg.vehicles_query():
                skip_leo = True
                msg = "FOTA-NIO登录失败，跳过Leo车型处理，请更新Cookie"
                logger.warning(msg)
                robot.set_payload("[软硬件零件号]Cookie过期", msg)
                robot.request()

            # 调试模式下直接运行
            if config.DEBUG:
                ecu_version_checker()
                break  # 调试模式下只运行一次

            # 确定触发时间并调度
            trigger_times = get_trigger_time()
            now_ts = time.time()

            # 检查是否需要立即执行
            if any(abs(ts - now_ts) < 60 for ts in trigger_times):
                logger.info("立即执行程序...")
                ecu_version_checker()
            else:
                # 按计划调度
                scheduler = sched.scheduler(time.time, time.sleep)
                for ts in trigger_times:
                    if ts > now_ts - 60:  # 允许60秒误差
                        scheduler.enterabs(ts, 1, ecu_version_checker)
                scheduler.run()

    except KeyboardInterrupt:
        logger.info("用户终止程序")
    except Exception as e:
        logger.error(f"程序异常终止: {str(e)}")
        logger.error(f"错误堆栈: {traceback.format_exc()}")
