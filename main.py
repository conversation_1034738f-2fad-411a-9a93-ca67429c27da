# !/usr/bin/python3
import datetime
import json
import os
import sched
import time
from typing import Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache
import threading

import config
import login_nio
import get_phoenix_info
import get_sam_info
import get_fota_info
import get_tvas_info
import parse_spreadsheet
import vehicle_sw_version_table
import robot_request
from log_config import logger

# 导入系统配置
try:
    import system_config
    SYSTEM_OPTIMIZED = True
    logger.info("已加载系统优化配置")
except ImportError:
    SYSTEM_OPTIMIZED = False
    logger.warning("未找到系统配置文件，使用默认配置")

# 全局缓存变量
_phoenix_cache = {}
_sam_cache = {}
_cache_lock = threading.Lock()

# 性能监控变量
_performance_stats = {
    'cache_hits': 0,
    'cache_misses': 0,
    'total_requests': 0,
    'processing_times': []
}

# 缓存装饰器
def cache_result(cache_dict, key_func=None):
    def decorator(func):
        def wrapper(*args, **kwargs):
            global _performance_stats
            _performance_stats['total_requests'] += 1

            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = str(args) + str(sorted(kwargs.items()))

            with _cache_lock:
                if cache_key in cache_dict:
                    _performance_stats['cache_hits'] += 1
                    logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                    return cache_dict[cache_key]

            # 执行函数并缓存结果
            _performance_stats['cache_misses'] += 1
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()

            with _cache_lock:
                cache_dict[cache_key] = result
                _performance_stats['processing_times'].append(end_time - start_time)
                logger.debug(f"Cache stored for {func.__name__}: {cache_key}, took {end_time - start_time:.3f}s")

            return result
        return wrapper
    return decorator

def print_performance_stats():
    """打印性能统计信息"""
    global _performance_stats
    total_requests = _performance_stats['total_requests']
    cache_hits = _performance_stats['cache_hits']
    cache_misses = _performance_stats['cache_misses']
    processing_times = _performance_stats['processing_times']

    if total_requests > 0:
        hit_rate = (cache_hits / total_requests) * 100
        logger.info(f"=== 性能统计 ===")
        logger.info(f"总请求数: {total_requests}")
        logger.info(f"缓存命中: {cache_hits}")
        logger.info(f"缓存未命中: {cache_misses}")
        logger.info(f"缓存命中率: {hit_rate:.2f}%")

        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            logger.info(f"平均处理时间: {avg_time:.3f}s")
            logger.info(f"最大处理时间: {max_time:.3f}s")
            logger.info(f"最小处理时间: {min_time:.3f}s")
        logger.info(f"===============")


def is_nt25_vehicle(vehicle_model: str) -> bool:
    """
    判断是否为NT2.5车型
    只有特定车型的G1.F版本才是NT2.5
    """
    if not vehicle_model:
        return False

    # 只有这些车型的G1.F版本才是NT2.5
    nt25_vehicle_types = ["ET5", "ET7", "Libra", "Orion", "Sirius"]

    # 检查是否包含G1.F且车型在NT2.5列表中
    if "G1.F" in vehicle_model:
        for vehicle_type in nt25_vehicle_types:
            if vehicle_type in vehicle_model:
                return True

    return False


def get_target_version_for_vehicle(vehicle_model: str) -> str:
    """
    根据车型选择对应的目标版本
    """
    if not vehicle_model:
        return config.NT2_VERSION

    # 检查是否是NT2.5车型（G1.F版本）
    if is_nt25_vehicle(vehicle_model):
        return config.NT25_VERSION

    # 默认返回NT2版本
    return config.NT2_VERSION

'''
* NT2
'''


# 缓存Phoenix系统的版本列表查询
@cache_result(_phoenix_cache, lambda vehicle_model: f"vehicle_release_{vehicle_model}")
def get_cached_vehicle_release(vehicle_model: str):
    """缓存的车型版本列表查询"""
    return phoenix.get_vehicle_release(vehicle_model)

# 缓存Phoenix系统的软件包列表查询
@cache_result(_phoenix_cache, lambda release_plan_id: f"package_list_{release_plan_id}")
def get_cached_package_list(release_plan_id: str):
    """缓存的软件包列表查询"""
    return phoenix.get_package_list(release_plan_id)

# 缓存Phoenix系统的软件包详情查询
@cache_result(_phoenix_cache, lambda package_id: f"package_detail_{package_id}")
def get_cached_package_detail(package_id: str):
    """缓存的软件包详情查询"""
    return phoenix.get_package_detail(package_id)

# 缓存SAM系统的VFR列表查询
@cache_result(_sam_cache, lambda brand: f"vfr_list_{brand}")
def get_cached_nt3_vfr_list(brand: str):
    """缓存的NT3 VFR列表查询"""
    return sam.get_nt3_vfr_list(brand)

# 动态获取缓存大小
def get_cache_size(cache_type: str, default: int) -> int:
    """获取缓存大小，优先使用系统配置"""
    if SYSTEM_OPTIMIZED:
        return system_config.get_cache_size(cache_type)
    return default

# 缓存车型版本匹配结果 - 适配16GB内存，增加缓存大小
@lru_cache(maxsize=get_cache_size('vehicle_model', 256))
def get_target_version_for_vehicle_cached(vehicle_model: str) -> str:
    """缓存的车型目标版本获取"""
    return get_target_version_for_vehicle(vehicle_model)

# 缓存版本比较结果 - 版本比较频繁，增加缓存
@lru_cache(maxsize=get_cache_size('version_compare', 1024))
def compare_versions_cached(version1: str, version2: str) -> bool:
    """缓存的版本比较"""
    return compare_versions(version1, version2)

# 缓存车型平台判断结果 - 车型种类有限，适中缓存
@lru_cache(maxsize=get_cache_size('nt25_vehicle', 128))
def is_nt25_vehicle_cached(vehicle_model: str) -> bool:
    """缓存的NT2.5车型判断"""
    return is_nt25_vehicle(vehicle_model)

# 缓存PN有效性检查 - PN数量较多，增加缓存
@lru_cache(maxsize=get_cache_size('pn_validation', 2048))
def is_valid_pn_cached(pn: str) -> bool:
    """缓存的PN有效性检查"""
    return is_valid_pn(pn)

# 缓存调试包检查 - 调试包检查频繁，增加缓存
@lru_cache(maxsize=get_cache_size('debug_package', 1024))
def is_debug_package_cached(pn: str) -> bool:
    """缓存的调试包检查"""
    return is_debug_package(pn)

# 缓存乱码检查 - 乱码检查频繁，增加缓存
@lru_cache(maxsize=get_cache_size('messy_code', 2048))
def is_messy_code_cached(pn: str) -> bool:
    """缓存的乱码检查"""
    return is_messy_code(pn)

def get_release_scope(sw_version: str) -> Optional[str]:
    """
    {
        "id": "0",
        "key": "FR-52222",
        "ecu": "ADC",
        "category": "EU",
        "swType": "EU",
        "swPn": "P0271673",
        "swRevision": "AC",
        "releaseScope": "ET7 G1.1-BL0.9.0",
        "releaseDate": "",
        "status": "In Release",
        "swPackage": "true",
        "dbcVersion": "v1.0.0",
        "notes": "",
        "packMethod": "excel",
        "edms": "28",
        "specialPn": true
    },
    """
    sw_parts = sw_version.split()
    sw_pn = sw_parts[0]
    sw_revision = sw_parts[-1]

    release_scope = None
    revision_list = phoenix.get_revision_list(sw_pn)
    if revision_list is None:
        return None

    for revision_dict in revision_list:
        tmp_sw_revision = revision_dict.get("swRevision")

        if tmp_sw_revision and tmp_sw_revision == sw_revision:
            release_scope = revision_dict.get("releaseScope")
            break
    return release_scope


def get_hardware_pn_list(hw_list: list, changed_hw_revs: list) -> list:
    res_list = []
    for hw_dict in hw_list:
        # {
        #     "pn": "P0279342",
        #     "vehicle": "Orion G1.2",
        #     "summary": "",
        #     "revisions": [
        #         {
        #             "key": "FR-53980",
        #             "name": "AA",
        #             "specialPn": [],
        #             "changeStatus": 0
        #         }
        #     ]
        # },
        pn = hw_dict.get("pn")
        revisions = hw_dict.get("revisions")
        for revision in revisions:
            res_list.append(pn + " " + revision.get("name"))

    # [
    #     "+P0281840 AE",
    #     "+P0216753 AI"
    # ]
    for changed_hw_rev in changed_hw_revs:
        first_char = changed_hw_rev[0]
        if first_char == "+":
            res_list.append(changed_hw_rev.split(first_char)[-1])
        # else:
        #     res_list.remove(changed_hw_rev.split(first_char)[-1])

    return res_list


def parse_package_detail(package_id: str):
    package_detail_list = get_cached_package_detail(package_id)
    if package_detail_list is None:
        return None

    """
    ecu_dict = {
        "ecu_1":
            [
                ("software_pn_1", ["hardware_pn_1", "hardware_pn_2"]),
                ("software_pn_2", ["hardware_pn_2", "hardware_pn_2"])
            ],
        "ecu_2":
            [
                ("software_pn_1", ["hardware_pn_1", "hardware_pn_2"]),
            ]
    }
    """
    ecu_dict = {}
    for package_detail in package_detail_list:
        sub_category = str(package_detail.get("subCategory", "")).lower()
        if "calib" in sub_category or "cal" in sub_category:
            if "100_calb" not in sub_category:
                continue

        ecu_name = package_detail.get("ecuMark")
        software_pn = package_detail.get("swPn") + " " + package_detail.get("swRevision")
        changed_hw_revs = package_detail.get("changedHwRevs")
        hw_list = package_detail.get("hwList")
        hardware_pn_list = get_hardware_pn_list(hw_list, changed_hw_revs)

        ecu_dict.setdefault(ecu_name, []).append((software_pn, hardware_pn_list))

    need_to_add_key = []
    need_to_delete_key = []
    for key in ecu_dict.keys():
        key_list = []
        if "/" in key:
            key_list = key.split("/")
            need_to_delete_key.append(key)
        elif "\\" in key:
            key_list = key.split("\\")
            need_to_delete_key.append(key)
        elif "+" in key:
            key_list = key.split("+")
            need_to_delete_key.append(key)
        for i in key_list:
            need_to_add_key.append((i, key))
    for t in need_to_add_key:
        new_key = t[0]
        delete_key = t[-1]
        if new_key in ecu_dict.keys():
            ecu_dict[new_key] += ecu_dict[delete_key]
        else:
            ecu_dict[new_key] = ecu_dict[delete_key]
    for delete_key in need_to_delete_key:
        del ecu_dict[delete_key]

    return ecu_dict


# 从标识报告中获取 ECU 的版本
def get_ecu_version_by_did(vin: str) -> Optional[dict]:
    path = ""
    did_sheet_dir = os.path.join(dir_path, "did_sheet")
    for file in os.listdir(did_sheet_dir):
        if vin in file:
            path = os.path.join(did_sheet_dir, file)
            break
    if not path:
        logger.warning(f"No did sheet for {vin}")
        return None

    logger.debug(f"did sheet path: {path}")
    did = parse_spreadsheet.DID(path)
    return did.get_ecu_version()


# 从TVAS上获取 ECU 的版本
# def get_ecu_version_by_tvas(vin: str) -> Optional[dict]:
#     global get_tvas_info_request
#     if get_tvas_info_request.query_by_vin(vin):
#         return get_tvas_info_request.query_ecu_version(vin)
#     else:
#         return None


# 10 或 13位 时间戳 转成 日期时间字符串
def get_date_from_timestamp(timestamp):
    if timestamp is None:
        return "--"
    datetime_struct = datetime.datetime.fromtimestamp(int(str(timestamp)[0:10]))
    return datetime.datetime.strftime(datetime_struct, "%Y-%m-%d %H:%M:%S")


def get_major_version(vehicle: str, engineer_version: str, bl_version: str):
    sw_release_list = phoenix.get_vehicle_release(vehicle)
    if sw_release_list is None:
        return None, None

    release_plan_id = None
    matched_engineer_version_list = []
    for sw_dict in sw_release_list:
        """
        {
            "id": "830",
            "key": "FR-609857",
            "platform": "NT2",
            "vehicle": "Lyra G1.1",
            "os": "BL2.6.0",
            "swPackageEngineerVersion": "Lyra.G1.1.AP.01",
            "swBaselineConfigDate": "",
            "swConfigFrozenDate": "",
            "plmReleaseDate": "2024-04-30",
            "releaseToUser": "",
            "swVariantPn": "V0078517",
            "swVariantRev": "",
            "majorRev": "BL2.6.0",
            "minorRev": "",
            "status": "In Validation",
            "note": "",
            "enabled": true,
            "releaseConfirm": true,
            "taskStatus": "",
            "quickTestVfiTime": "",
            "autoFuncVfiTime": ""
        },
        """

        tmp_engineer_version = sw_dict.get("swPackageEngineerVersion")
        tmp_major_rev = sw_dict.get("majorRev")
        if tmp_engineer_version == engineer_version:
            release_plan_id = sw_dict.get("id")
            if tmp_major_rev == bl_version:
                return release_plan_id, tmp_major_rev
            else:
                # 只 engineer_version 相等
                matched_engineer_version_list.append((release_plan_id, tmp_major_rev))

    if len(matched_engineer_version_list) == 1:
        return matched_engineer_version_list[0]

    return release_plan_id, None


def get_major_version_by_bl_version(vehicle: str, bl_version: str):
    substrings = [".E", ".UAE", "RHD"]
    if any(substring in vehicle for substring in substrings):
        vehicle_model_and_ga_version = vehicle
    else:
        vehicle_model = vehicle.split()[0]
        ga_version_list = config.GA_VERSION.get(vehicle_model)
        if ga_version_list and vehicle in ga_version_list:
            # 直接使用传入的vehicle参数，而不是使用max()选择
            # 这样可以确保使用正确的车型版本（如Sirius G1.1而不是Sirius G1.F）
            vehicle_model_and_ga_version = vehicle
        else:
            logger.warning(f"GA version not found for {vehicle}")
            return None, None

    logger.debug(f"vehicle_model_and_ga_version: {vehicle_model_and_ga_version}")
    sw_release_list = get_cached_vehicle_release(vehicle_model_and_ga_version)

    if sw_release_list is None or len(sw_release_list) < 1:
        return None, None

    # 添加调试信息：显示Phoenix系统返回的所有版本列表
    logger.info(f"🔍 Phoenix系统 {vehicle_model_and_ga_version} 的版本列表:")
    for i, sw_dict in enumerate(sw_release_list):
        vehicle_type = sw_dict.get("vehicle", "")
        version_name = sw_dict.get("majorRev", "")
        release_id = sw_dict.get("id", "")
        logger.info(f"   {i+1}. vehicle_type: '{vehicle_type}', name: '{version_name}', id: {release_id}")
    logger.info(f"🔍 正在查找车型: '{vehicle_model_and_ga_version}', 版本: '{bl_version}'")

    for sw_dict in sw_release_list:
        """
        {
            "id": "830",
            "key": "FR-609857",
            "platform": "NT2",
            "vehicle": "Lyra G1.1",
            "os": "BL2.6.0",
            "swPackageEngineerVersion": "Lyra.G1.1.AP.01",
            "swBaselineConfigDate": "",
            "swConfigFrozenDate": "",
            "plmReleaseDate": "2024-04-30",
            "releaseToUser": "",
            "swVariantPn": "V0078517",
            "swVariantRev": "",
            "majorRev": "BL2.6.0",
            "minorRev": "",
            "status": "In Validation",
            "note": "",
            "enabled": true,
            "releaseConfirm": true,
            "taskStatus": "",
            "quickTestVfiTime": "",
            "autoFuncVfiTime": ""
        },
        """
        temp_release_plan_id = sw_dict.get("id")
        tmp_major_rev = sw_dict.get("majorRev")
        if tmp_major_rev == bl_version:
            vehicle_type = sw_dict.get("vehicle", "")
            logger.info(f"🎯 车型+版本精确匹配: {vehicle_model_and_ga_version} + {bl_version} -> {vehicle_type} + {tmp_major_rev}, id: {temp_release_plan_id}")
            return temp_release_plan_id, tmp_major_rev

    latest_sw_dict = sw_release_list[0]
    logger.warning(f"未找到目标版本 {bl_version}，使用最新版本: {latest_sw_dict.get('majorRev')}")
    return latest_sw_dict.get("id"), latest_sw_dict.get("majorRev")


def get_hardware_compatibility_dict(release_plan_id) -> Optional[dict]:
    if release_plan_id is None:
        return None

    package_list = get_cached_package_list(release_plan_id)
    if package_list is None:
        return None

    """
    {
        "id": "6482",
        "platform": "NT2",
        "vehicle": "Lyra G1.1",
        "os": "BL2.4.5",
        "name": "NT2-Lyra G1.1-BL2.4.5-27",
        "pkgPlan": {
            "id": "5357",
            "name": "build23-AD领航去除SPM",
            "autoBuild": false
        },
        "startTime": "2024-03-20 17:16:17",
        "endTime": "1970-01-01 08:00:01",
        "status": "In Release",
        "swBaseline": "NT2-Lyra G1.1-BL2.4.5-26",
        "tag": "PLM Release",
        "swVariantPn": "V0078517",
        "swVariantRev": "CS",
        "autoBuild": false,
        "bomCnt": 0,
        "execMethod": "manual",
        "buildUser": "wei.zhang32",
        "notifyTime": "2024-03-20 17:19:07",
        "releasePlanId": "796"
    }
    """

    # package_id = None
    # package_name = None
    # latest_package_id = None
    # for package in package_list:
    #     package_name = package.get("name")
    #     tmp_id = package.get("id")
    #     latest_package_id = tmp_id
    #     tag = package.get("tag")
    #
    #     if tag == "PLM Release":
    #         package_id = tmp_id
    #         break
    # if package_id is None:
    #     package_id = latest_package_id
    # logger.debug(f"{package_name} package_id: {package_id}")

    package_id = package_list[0].get("id")
    package_name = package_list[0].get("name")
    logger.debug(f"{package_name} package_id: {package_id}")

    if package_id:
        return parse_package_detail(package_id)
    else:
        return None


def update_summary_table_record_dict(fields: dict, record_id: str):
    global summary_table_record_dict

    # 验证 record_id 是否有效
    if not record_id or record_id.strip() == "":
        logger.warning(f"Invalid record_id provided: '{record_id}', skipping update")
        return

    if record_id in summary_table_record_dict.keys():
        summary_table_record_dict[record_id]["fields"].update(fields)
    else:
        summary_table_record_dict[record_id] = {"fields": fields, "record_id": record_id}


def add_detail_table_records(records: list):
    for record in records:
        detail_table_records.append(
            {
                "fields": record
            }
        )


def compare_versions(version1, version2):
    v1_num = version1.split("BL")[-1]
    v2_num = version2.split("BL")[-1]
    v1 = v1_num.split('.')
    v2 = v2_num.split('.')
    for i in range(3):
        if int(v1[i]) < int(v2[i]):
            return False
        elif int(v1[i]) > int(v2[i]):
            return True
    return True


def is_messy_code(pn: str):
    if not pn:
        return False

    pn_list = pn.strip().split()
    if len(pn_list) != 2:
        logger.warning(f"PN length is {len(pn_list)} != 2, is messy code!")
        return True

    return not all(x.isalnum() for x in pn_list)


def is_valid_pn(pn: str):
    if not pn:
        return False
    if is_messy_code(pn):
        return False
    return True


def is_debug_package(pn: str):
    if pn.startswith("D") or pn.startswith("P4"):
        return True
    elif pn.endswith("ZZ"):
        return True
    return False


def process_single_vehicle(vin: str, vin_values: dict) -> tuple:
    """处理单个车辆的信息，返回处理结果"""
    try:
        logger.info(f"🚗 开始处理车辆: {vin}")

        # 创建本地的记录字典，避免全局变量冲突
        local_summary_record = {}
        local_detail_records = []
        local_raw_ecu_records = []

        # 临时设置全局变量以兼容现有函数
        global summary_table_record_dict, detail_table_records
        old_summary = summary_table_record_dict if 'summary_table_record_dict' in globals() else {}
        old_detail = detail_table_records if 'detail_table_records' in globals() else []

        summary_table_record_dict = local_summary_record
        detail_table_records = local_detail_records

        try:
            vehicle_model, vehicle_platform = get_vehicle_model_platform(vin, vin_values)
            logger.info(f"🔍 VIN {vin}: 识别的车型='{vehicle_model}', 平台={vehicle_platform}")

            if vehicle_platform:
                # NT2.5车型单独处理
                if vehicle_platform == "NT2.5":
                    logger.info(f"VIN {vin}: NT2.5车型详细信息 - TVAS返回的车型: '{vehicle_model}'")
                    raw_data = check_ecu_version_for_one_vehicle(vin, vin_values, vehicle_platform)
                    if raw_data:
                        local_raw_ecu_records.extend(raw_data)
                elif "NT2" in vehicle_platform:
                    # 普通NT2车型
                    logger.info(f"VIN {vin}: NT2.0车型详细信息 - TVAS返回的车型: '{vehicle_model}'")
                    raw_data = check_ecu_version_for_one_vehicle(vin, vin_values, vehicle_platform)
                    if raw_data:
                        local_raw_ecu_records.extend(raw_data)
                elif "NT3" in vehicle_platform:
                    if vehicle_model and vehicle_model.lower() in ["dom", "blanc", "rosa"]:
                        raw_data = check_nt3_ecu_version_for_one_vehicle("alps", vin, vin_values, vehicle_model)
                        if raw_data:
                            local_raw_ecu_records.extend(raw_data)
                    elif vehicle_model and vehicle_model.lower() in ["leo", "cetus"]:
                        if skip_leo:
                            return vin, None, [], []
                        raw_data = check_nt3_ecu_version_for_one_vehicle("nio", vin, vin_values, vehicle_model)
                        if raw_data:
                            local_raw_ecu_records.extend(raw_data)
                    else:
                        logger.warning(f"Unknown vehicle_model: {vehicle_model}")
                        return vin, None, [], []
                else:
                    logger.warning(f"Unknown vehicle_platform: {vehicle_platform}")
                    return vin, None, [], []
            else:
                return vin, None, [], []

            logger.info(f"完成处理车辆: {vin}, 原始ECU记录数: {len(local_raw_ecu_records)}")
            return vin, dict(local_summary_record), list(local_detail_records), list(local_raw_ecu_records)

        finally:
            # 恢复全局变量
            summary_table_record_dict = old_summary
            detail_table_records = old_detail

    except Exception as e:
        logger.error(f"处理车辆 {vin} 时发生错误: {str(e)}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return vin, None, [], []



def check_ecu_version_for_one_vehicle(vin: str, vin_values: dict, vehicle_platform: str = None):
    # 获取软硬件零件号
    ecu_version_info = None
    use_did = vin_values.get("use_did")
    if use_did:
        ecu_version_info = get_ecu_version_by_did(vin)

    # 若本地没有标识文件，尝试从 TVAS 上抓取
    if ecu_version_info is None:
        global get_tvas_info_request
        ecu_version_info = get_tvas_info_request.query_ecu_version(vin)
        if ecu_version_info is None:
            logger.warning(f"Can't get ecu_version for {vin}")
            update_summary_table_record_dict(
                {
                    "VIN": vin,
                    "软件包工程版本": "未知",
                    "版本迭代号": "未知",
                    "硬件是否都兼容": "不确定",
                    "各ECU软件都对齐至整车版本": "不确定",
                    "采样时间": None,
                    "备注": "获取不到 ECU 的软硬件零件号"
                },
                vin_values.get("record_id")
            )
            return []

    sample_time = ecu_version_info.get("sample_time")  # s
    sample_time_ms = int(sample_time) * 1000
    ecu_version_dict = ecu_version_info.get("ecu_version_map", {})
    ecu_version_list = list(ecu_version_dict.values())

    # 收集原始ECU数据
    vehicle_model = vin_values.get("vehicle_model", "")
    raw_ecu_data = collect_raw_ecu_data(vin, ecu_version_list, vehicle_model, vehicle_platform, sample_time_ms)
    
    # 将原始数据添加到全局列表中
    global detail_table_records
    if hasattr(detail_table_records, 'extend'):
        detail_table_records.extend(raw_ecu_data)
    else:
        if not hasattr(process_single_vehicle, '_raw_ecu_records'):
            process_single_vehicle._raw_ecu_records = []
        process_single_vehicle._raw_ecu_records.extend(raw_ecu_data)
    
    # 在函数最后返回原始数据
    return raw_ecu_data

def check_compatibility(hardware_compatibility_dict: dict, ecu_version_list: list, one_record_dict: dict, vin: str,
                        vin_values: dict):
    if hardware_compatibility_dict is None:
        logger.warning(f"Can't get hardware compatibility for {vin}")
        one_record_dict["硬件是否都兼容"] = "不确定"
        one_record_dict["各ECU软件都对齐至整车版本"] = "不确定"
        one_record_dict["备注"] = "获取不到此版本的软硬件兼容表"
        update_summary_table_record_dict(
            one_record_dict,
            vin_values.get("record_id")
        )
        return

    # 软硬件兼容性检查
    replace_hardware_list = []
    upgrade_software_list = []
    for value in ecu_version_list:
        ecu_name = value.get("ecu")
        if ecu_name not in hardware_compatibility_dict.keys():
            continue

        """
        软硬件的兼容情况各有三种：
        硬件：兼容（H√），不兼容（H×），无法获取零件号（H-）
        硬件：兼容（S√），不兼容（S×），无法获取零件号（S-）
        两两组合共有9种情况，根据实际需求进行合并：
        1. H-S√ H-S× H-S- 硬件不兼容 无法获取硬件零件号或乱码

        2. H√S√ 软硬件都兼容
        3. H√S× H√S- 硬件兼容 软件版本未对齐

        4. H×S√ H×S× H×S- 硬件不兼容
        """
        current_software_pn = value.get("software_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())
        current_hardware_pn = value.get("hardware_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())

        hardware_pn_set = set()
        software_pn_set = set()
        software_pn_and_hardware_pn_list = hardware_compatibility_dict[ecu_name]
        for software_pn, hardware_pn_list in software_pn_and_hardware_pn_list:
            hardware_pn_set.update(set(hardware_pn_list))
            software_pn_set.add(software_pn)

        # 对于Dom车型，进行硬件零件号过滤
        vehicle_model = vin_values.get("vehicle_model", "").lower() if vin_values.get("vehicle_model") else ""
        if vehicle_model == "dom":
            # 过滤掉无效的硬件零件号
            filtered_hardware_pn_set = set()
            for hw_pn in hardware_pn_set:
                if hw_pn and is_valid_pn(hw_pn):
                    filtered_hardware_pn_set.add(hw_pn)

            if len(filtered_hardware_pn_set) != len(hardware_pn_set):
                logger.debug(f"Dom车型 {ecu_name} ECU: 过滤前数量={len(hardware_pn_set)}, 过滤后数量={len(filtered_hardware_pn_set)}")

            hardware_pn_set = filtered_hardware_pn_set

        tmp_hardware_dict = {
            "VIN": vin,
            "硬件不兼容的ECU": ecu_name,
            "当前硬件零件号": current_hardware_pn,
            "可更换的硬件": list(hardware_pn_set),
        }
        if not current_hardware_pn:
            # 1. H-S√ H-S× H-S- 硬件零件号为空
            logger.debug(f"{ecu_name} 硬件零件号为空")
            tmp_hardware_dict["备注"] = "硬件零件号为空"
            replace_hardware_list.append(tmp_hardware_dict)
            continue
        elif is_messy_code(current_hardware_pn):
            # 1. H-S√ H-S× H-S- 硬件零件号乱码
            logger.debug(f"{ecu_name} 硬件零件号乱码")
            tmp_hardware_dict["备注"] = "硬件零件号乱码"
            replace_hardware_list.append(tmp_hardware_dict)
            continue

        hardware_matched = False
        software_matched = False
        tmp_software_dict = None

        # 预先分割current_software_pn避免重复操作
        current_software_parts = current_software_pn.split() if current_software_pn else []
        current_software_num = current_software_parts[0] if current_software_parts else ""
        current_software_revision = current_software_parts[-1] if current_software_parts else ""

        for software_pn, hardware_pn_list in software_pn_and_hardware_pn_list:
            target_software_parts = software_pn.split()
            target_software_num = target_software_parts[0]
            target_software_revision = target_software_parts[-1]

            if current_hardware_pn in hardware_pn_list or not hardware_pn_list:
                # 硬件兼容
                hardware_matched = True

                if is_valid_pn(current_software_pn) and \
                        (current_software_num == target_software_num or is_debug_package(
                            current_software_pn)):
                    #  2. H√S√ 软硬件都兼容
                    software_matched = True

                    if not is_debug_package(current_software_pn) and \
                            current_software_revision < target_software_revision:
                        logger.debug(f"{ecu_name} 软件兼容，但版本未对齐")
                        # 软件兼容 但未对齐
                        tmp_software_dict = {
                            "VIN": vin,
                            "软件未对齐的ECU": ecu_name,
                            "当前软件零件号": current_software_pn,
                            "目标软件零件号": [software_pn],
                            "备注": "软件兼容，但版本未对齐",
                        }
                    break

        if hardware_matched and not software_matched:
            # 3. H√S× H√S- 硬件兼容 软件不兼容
            tmp_software_dict = {
                "VIN": vin,
                "软件未对齐的ECU": ecu_name,
                "当前软件零件号": current_software_pn,
                "目标软件零件号": list(software_pn_set)
            }
            if not current_software_pn:
                tmp_software_dict["备注"] = "软件零件号为空"
            elif is_messy_code(current_software_pn):
                tmp_software_dict["备注"] = "软件零件号乱码"
            else:
                tmp_software_dict["备注"] = "软件不兼容"

        # 添加软件未对齐的 ECU
        if tmp_software_dict:
            upgrade_software_list.append(tmp_software_dict)

        if not hardware_matched:
            replace_hardware_list.append({
                "VIN": vin,
                "硬件不兼容的ECU": ecu_name,
                "当前硬件零件号": current_hardware_pn,
                "可更换的硬件": list(hardware_pn_set),
            })

    if len(replace_hardware_list) > 0:
        one_record_dict["硬件是否都兼容"] = "不兼容"
        one_record_dict["各ECU软件都对齐至整车版本"] = "未对齐"
        replace_hardware_list.sort(key=lambda x: x["硬件不兼容的ECU"])
        add_detail_table_records(replace_hardware_list)

    if len(upgrade_software_list) > 0:
        one_record_dict["各ECU软件都对齐至整车版本"] = "未对齐"
        upgrade_software_list.sort(key=lambda x: x["软件未对齐的ECU"])
        add_detail_table_records(upgrade_software_list)

    update_summary_table_record_dict(
        one_record_dict,
        vin_values.get("record_id")
    )


'''
* NT3
'''


def get_nt3_hardware_pn_list(hw_list: list, changed_hw_revs: list, ecu_name: str):
    res_list = []
    for hw_dict in hw_list:
        """
        {
            "ecu_name": "CDF",
            "label": "CDF",
            "hw_change_type": "no_change",
            "children": [
                {
                    "label": "P0337429",
                    "children": [
                        {
                            "pn": "P0337429",
                            "label": "AD",
                            "hw_change_type": "no_change",
                            "children": []
                        },
                        {
                            "pn": "P0337429",
                            "label": "AE",
                            "hw_change_type": "no_change",
                            "children": []
                        },
                        {
                            "pn": "P0337429",
                            "label": "AF",
                            "hw_change_type": "no_change",
                            "children": []
                        },
                        {
                            "pn": "P0337429",
                            "label": "AG",
                            "hw_change_type": "no_change",
                            "children": []
                        },
                        {
                            "pn": "P0337429",
                            "label": "AH",
                            "hw_change_type": "no_change",
                            "children": []
                        }
                    ]
                }
            ]
        }
        """
        hw_ecu_name = hw_dict.get("ecu_name")
        if hw_ecu_name.lower() != ecu_name.lower():
            continue

        hw_pn_list = hw_dict.get("children", [])
        for hw_pn_dict in hw_pn_list:
            hw_pn = hw_pn_dict.get("label", "")
            revisions = hw_pn_dict.get("children", [])
            for revision_dict in revisions:
                revision = revision_dict.get("label", "")
                res_list.append(hw_pn + " " + revision)

    """
    [
        "+CDF P0337429 AI"
    ]
    """
    if changed_hw_revs and len(changed_hw_revs) > 0:
        for changed_hw_rev in changed_hw_revs:
            first_char = changed_hw_rev[0]
            if first_char == "+":
                hw_ecu_name = changed_hw_rev.split()[0][1:]
                if hw_ecu_name.lower() != ecu_name.lower():
                    continue

                hw_pn_revision = changed_hw_rev.split(hw_ecu_name)[-1].strip()
                if hw_pn_revision and hw_pn_revision not in res_list:
                    res_list.append(hw_pn_revision)

    return res_list


def parse_nt3_package_detail(package_detail_list: list):
    if package_detail_list is None:
        return None

    """
    ecu_dict = {
        "ecu_1":
            [
                ("software_pn_1", ["hardware_pn_1", "hardware_pn_2"]),
                ("software_pn_2", ["hardware_pn_2", "hardware_pn_2"])
            ],
        "ecu_2":
            [
                ("software_pn_1", ["hardware_pn_1", "hardware_pn_2"]),
            ]
    }
    """
    ecu_dict = {}
    for package_detail in package_detail_list:
        sub_category = str(package_detail.get("sub_category", "")).lower()
        if "calib" in sub_category or "cal" in sub_category:
            if "100_calb" not in sub_category:
                continue

        software_pn = package_detail.get("sw_pn") + " " + package_detail.get("current_revision")
        if software_pn is None:
            continue

        hw_list = package_detail.get("hw_list")
        if hw_list is None:
            continue
        changed_hw_revs = package_detail.get("changed_hw_list")

        original_ecu_name = package_detail.get("ecu_mark")
        ecu_name_list = original_ecu_name.split("+")
        for ecu_name in ecu_name_list:
            hardware_pn_list = get_nt3_hardware_pn_list(hw_list, changed_hw_revs, ecu_name)
            ecu_dict.setdefault(ecu_name, []).append((software_pn, hardware_pn_list))

    return ecu_dict


def get_package_detail(brand: str, package_name=None, vehicle_model=None):
    # 🔧 Dom车型调试：清除缓存确保获取最新数据
    is_dom_debug = vehicle_model and vehicle_model.lower().startswith("dom")
    if is_dom_debug:
        logger.info(f"🔍 Dom车型调试: 清除SAM缓存，重新获取VFR数据")
        # 清除SAM缓存
        cache_key = f"vfr_list_{brand}"
        if cache_key in _sam_cache:
            del _sam_cache[cache_key]
            logger.info(f"✅ 已清除SAM缓存: {cache_key}")

    nt3_vfr_list = get_cached_nt3_vfr_list(brand)

    # 🔧 Dom车型调试：详细记录VFR数据
    if is_dom_debug and nt3_vfr_list:
        logger.info(f"🔍 Dom车型调试: SAM系统返回的所有VFR数据:")
        for i, vfr in enumerate(nt3_vfr_list):
            logger.info(f"   VFR {i+1}: vehicle_type='{vfr.get('vehicle_type', '')}', name='{vfr.get('name', '')}', id={vfr.get('id', '')}")
        logger.info(f"🔍 Dom车型调试: 当前查找条件 - vehicle_model='{vehicle_model}', package_name='{package_name}'")

    # 显示SAM系统版本列表（类似NT2车型的Phoenix系统版本列表）
    logger.info(f"🔍 SAM系统 {vehicle_model} 的版本列表:")
    if not nt3_vfr_list:
        logger.warning(f"⚠️ SAM系统没有返回 {brand} 品牌的VFR列表！")
    else:
        # 只显示与当前车型相关的版本 - 使用精确匹配避免Dom车型匹配错误
        relevant_vfrs = []
        for vfr in nt3_vfr_list:
            vehicle_type = vfr.get("vehicle_type", "")
            # 🔧 严格匹配：确保Dom G1.1不会匹配到Dom G1.3或其他车型
            # 使用精确匹配而不是模糊匹配
            if vehicle_model and vehicle_type:
                if vehicle_model.lower() == vehicle_type.lower():
                    # 完全匹配
                    relevant_vfrs.append(vfr)
                elif vehicle_model.split()[0].lower() == vehicle_type.split()[0].lower():
                    # 基础车型匹配（如Dom匹配Dom），但要显示所有版本供参考
                    relevant_vfrs.append(vfr)

        if relevant_vfrs:
            for i, vfr in enumerate(relevant_vfrs):
                logger.info(f"   {i+1}. vehicle_type: '{vfr.get('vehicle_type', '')}', name: '{vfr.get('name', '')}', id: {vfr.get('id', '')}")
        else:
            logger.warning(f"⚠️ SAM系统中没有找到与 {vehicle_model} 相关的版本！")

    logger.info(f"🔍 正在查找车型: '{vehicle_model}', 版本: '{package_name}'")
    logger.info(f"📊 SAM系统VFR列表总数: {len(nt3_vfr_list) if nt3_vfr_list else 0}")

    # 显示所有VFR的车型信息，用于调试车型匹配问题
    if nt3_vfr_list:
        logger.info("📋 SAM系统所有VFR车型列表:")
        for i, vfr in enumerate(nt3_vfr_list):
            vehicle_type = vfr.get("vehicle_type", "")
            vfr_name = vfr.get("name", "")
            vfr_id = vfr.get("id", "")
            logger.info(f"   VFR {i+1}: vehicle_type='{vehicle_type}', name='{vfr_name}', id={vfr_id}")



    release_plan_id = None

    # 优先匹配车型+版本的组合 - 分两轮匹配，避免Dom G1.1匹配到Dom G1.3
    if vehicle_model and package_name and nt3_vfr_list:
        # 第一轮：优先寻找完全匹配（车型+版本都完全匹配）
        for vfr_dict in nt3_vfr_list:
            vehicle_type = vfr_dict.get("vehicle_type", "")
            vfr_version = vfr_dict.get("name", "")

            # 完全匹配检查：车型和版本都必须完全匹配
            if (vehicle_model.lower() == vehicle_type.lower() and
                vfr_version == package_name):
                release_plan_id = vfr_dict.get("id")
                logger.info(f"🎯 车型+版本完全匹配: {vehicle_model} + {package_name} -> {vehicle_type} + {vfr_version}, id: {release_plan_id}")
                logger.info(f"✅ SAM系统匹配到的完整车型: '{vehicle_type}' (版本: {vfr_version})")
                break

        # 第二轮：如果完全匹配失败，再尝试基础车型匹配
        if release_plan_id is None:
            logger.info(f"🔍 完全匹配失败，尝试基础车型匹配...")
            for vfr_dict in nt3_vfr_list:
                vehicle_type = vfr_dict.get("vehicle_type", "")
                vfr_version = vfr_dict.get("name", "")

                # 检查基础车型匹配，如 "Cetus" 匹配 "Cetus G1.1"
                # 提取基础车型名称（去掉版本后缀）
                base_vehicle_model = vehicle_model.split()[0].lower()  # 如 "Cetus" 或 "Dom"
                base_vehicle_type = vehicle_type.split()[0].lower()    # 如 "Cetus" 或 "Dom"

                if (base_vehicle_model == base_vehicle_type and
                    vfr_version == package_name):
                    release_plan_id = vfr_dict.get("id")
                    logger.info(f"🎯 基础车型+版本匹配: {vehicle_model} + {package_name} -> {vehicle_type} + {vfr_version}, id: {release_plan_id}")
                    logger.info(f"✅ SAM系统匹配到的完整车型: '{vehicle_type}' (版本: {vfr_version})")
                    break

    # 如果没有找到精确匹配，则按车型匹配，选择最新版本 - 同样分两轮匹配
    if release_plan_id is None and vehicle_model and nt3_vfr_list:
        # 第一轮：优先寻找完全匹配的车型（忽略版本）
        complete_matched_vfrs = []
        for vfr_dict in nt3_vfr_list:
            vehicle_type = vfr_dict.get("vehicle_type", "")
            if vehicle_model.lower() == vehicle_type.lower():
                complete_matched_vfrs.append(vfr_dict)
                logger.info(f"✅ 车型完全匹配(备选): '{vehicle_model}' <-> '{vehicle_type}'")

        # 第二轮：如果完全匹配失败，再尝试基础车型匹配
        base_matched_vfrs = []
        if not complete_matched_vfrs:
            logger.info(f"🔍 车型完全匹配失败，尝试基础车型匹配...")
            for vfr_dict in nt3_vfr_list:
                vehicle_type = vfr_dict.get("vehicle_type", "")
                # 检查基础车型匹配，如 "Cetus" 匹配 "Cetus G1.1"
                base_vehicle_model = vehicle_model.split()[0].lower()
                base_vehicle_type = vehicle_type.split()[0].lower()

                if base_vehicle_model == base_vehicle_type:
                    base_matched_vfrs.append(vfr_dict)
                    logger.info(f"✅ 基础车型匹配(备选): '{vehicle_model}' <-> '{vehicle_type}' (基础车型: {base_vehicle_model})")

        # 选择匹配结果：优先使用完全匹配，其次使用基础车型匹配
        matched_vfrs = complete_matched_vfrs if complete_matched_vfrs else base_matched_vfrs

        # 从匹配的VFR中选择第一个（通常是最新的）
        if matched_vfrs:
            selected_vfr = matched_vfrs[0]
            release_plan_id = selected_vfr.get("id")
            match_type = "完全匹配" if complete_matched_vfrs else "基础车型匹配"
            logger.info(f"🎯 车型{match_type}成功: {vehicle_model} -> {selected_vfr.get('vehicle_type')}, 版本: {selected_vfr.get('name')}, id: {release_plan_id}")
            logger.info(f"✅ SAM系统匹配到的完整车型: '{selected_vfr.get('vehicle_type')}' (版本: {selected_vfr.get('name')})")

    # 严格禁止仅按版本匹配 - 这是Dom车型匹配到Blanc数据的根本原因！
    # 绝对不允许忽略车型信息，仅按版本名称匹配，这会导致严重的车型数据混乱
    # elif release_plan_id is None and package_name:
    #     for vfr_dict in nt3_vfr_list:
    #         if package_name == vfr_dict["name"]:
    #             release_plan_id = vfr_dict.get("id")
    #             logger.info(f"✅ 版本匹配成功: {package_name}, id: {release_plan_id}")
    #             break

    # 严格禁止兜底方案 - 绝对不允许Dom匹配到Blanc等其他车型
    elif release_plan_id is None and nt3_vfr_list:
        logger.error(f"❌ 严重错误：车型 '{vehicle_model}' 在SAM系统中找不到匹配的VFR！")
        logger.error(f"   查找条件: brand={brand}, vehicle_model={vehicle_model}, package_name={package_name}")
        logger.error(f"   绝对禁止使用兜底方案，避免Dom车型匹配到Blanc等其他车型的数据")
        logger.error(f"   请检查SAM系统中是否存在该车型的VFR配置")
        return None

    package_detail = None
    if release_plan_id:
        logger.info(f"🎯 找到release_plan_id: {release_plan_id}")
        sw_config_list = sam.get_nt3_vfr_sw_config_list(brand, release_plan_id)
        logger.info(f"sw_config_list数量: {len(sw_config_list) if sw_config_list else 0}")
        if sw_config_list and len(sw_config_list) > 0:
            sw_config_id = sw_config_list[0].get("id")
            sw_name = sw_config_list[0].get("sw_name")
            base_version = sw_config_list[0].get("base_version")

            if base_version is None:
                base_version = sw_name
            if sw_config_id and base_version:
                logger.debug(f"sw_config_id: {sw_config_id}, base_version: {base_version}")
                package_detail = sam.get_package_detail(brand, sw_config_id, base_version)
        else:
            logger.warning(f"No sw_config_list found for brand={brand}, release_plan_id={release_plan_id}")
            logger.warning(f"这可能是因为该VFR版本还没有生成对应的软件配置包")
            # 不返回None，而是继续处理，让上层函数知道这种情况
            return "NO_SW_CONFIG"
    else:
        logger.error(f"❌ 没有找到匹配的release_plan_id!")
        logger.error(f"   查找条件: brand={brand}, vehicle_model={vehicle_model}, package_name={package_name}")
        logger.error(f"   这是Dom车型找不到硬件零件号的根本原因！")
        return None

    return package_detail


# 从 FOTA WEB 上获取 ECU 的版本
def get_ecu_version_by_fota_web(vin: str, brand: str):
    global fota_onvo_stg
    global fota_nio_stg
    if brand.lower() in ["dom", "blanc", "rosa", "alps", "onvo"]:
        vehicle_info_dict = fota_onvo_stg.query_vehicle_info(vin)
        if vehicle_info_dict and vehicle_info_dict.get("vid"):
            return fota_onvo_stg.query_ecu_version_by_fota(vehicle_info_dict.get("vid"))
    else:
        if skip_leo:
            return None
        vehicle_info_dict = fota_nio_stg.query_vehicle_info(vin)
        if vehicle_info_dict and vehicle_info_dict.get("vid"):
            return fota_nio_stg.query_ecu_version_by_fota(vehicle_info_dict.get("vid"))

    return None


def get_complete_vehicle_model_from_config(simplified_model: str) -> str:
    """
    根据config中的GA_VERSION定义，将简化车型名称转换为完整车型名称
    例如: "Cetus" -> "Cetus G1.1", "Dom" -> "Dom G1.1" (取第一个版本)
    """
    if not simplified_model:
        return simplified_model

    # 查找config中的GA_VERSION定义
    ga_versions = config.GA_VERSION.get(simplified_model)
    if ga_versions and len(ga_versions) > 0:
        # 取第一个版本作为默认版本
        complete_model = ga_versions[0]
        logger.info(f"🔄 根据config映射: '{simplified_model}' -> '{complete_model}'")
        return complete_model

    # 如果没有找到映射，返回原始名称
    logger.warning(f"⚠️ 未在config.GA_VERSION中找到车型: '{simplified_model}'")
    return simplified_model

def check_nt3_ecu_version_for_one_vehicle(brand: str, vin: str, vin_values: dict, vehicle_model: str = None):
    # 根据品牌和具体车型选择对应的版本
    logger.info(f"🚗 开始处理NT3车型: brand={brand}, vehicle_model='{vehicle_model}', vin={vin}")

    # 特别标记Dom车型的处理过程
    is_dom_vehicle = vehicle_model and vehicle_model.lower() == "dom"
    if is_dom_vehicle:
        logger.info(f"🔍 Dom车型特殊处理开始: {vin}")

    # 保存原始车型名称，用于后续版本选择
    original_vehicle_model = vehicle_model

    # 如果vehicle_model是简化名称，暂时不使用config默认值
    # 而是等待从ECU版本信息中提取完整车型信息
    if vehicle_model and ' G1.' not in vehicle_model:
        logger.info(f"⚠️ 车型名称 '{vehicle_model}' 缺少版本信息，将尝试从ECU版本信息中提取完整车型")
        if is_dom_vehicle:
            logger.info(f"🔍 Dom车型版本提取: 当前简化名称='{vehicle_model}', 需要获取完整版本信息")

    # 首先获取ECU版本信息，以便提取完整的车型信息
    ecu_version_list = None
    sample_time_ms = None
    package_global_version = None
    latest_engineer_version = None
    latest_bl_version = None

    # 获取软硬件零件号
    use_did = vin_values.get("use_did")
    if use_did:
        logger.info(f"📄 尝试从DID文件获取ECU版本信息: {vin}")
        if is_dom_vehicle:
            logger.info(f"🔍 Dom车型DID文件查询: {vin}")
        ecu_version_info = get_ecu_version_by_did(vin)
        logger.debug(json.dumps(ecu_version_info))
        if ecu_version_info:
            logger.info(f"✅ 成功从DID文件获取ECU版本信息: {vin}")
            ecu_version_list = list(ecu_version_info.get("ecu_version_map", {}).values())
            package_global_version = ecu_version_info.get("package_global_version")
            sample_time = ecu_version_info.get("sample_time")
            sample_time_ms = int(sample_time) * 1000
        else:
            logger.warning(f"⚠️ DID文件中未找到ECU版本信息: {vin}")
            if is_dom_vehicle:
                logger.warning(f"🔍 Dom车型DID文件查询失败: {vin}")

    if ecu_version_list is None:
        logger.info(f"从TVAS获取ECU版本信息失败，尝试从FOTA系统获取: {vin}")
        ecu_version_info = get_ecu_version_by_fota_web(vin, brand)

        if ecu_version_info:
            logger.info(f"✓ 成功从FOTA系统获取到ECU版本信息: {vin}")
            logger.debug(json.dumps(ecu_version_info))
            ecu_version_list = list(ecu_version_info.get("ecu_version_map", {}).values())
            package_global_version = ecu_version_info.get("package_global_version")
            sample_time = ecu_version_info.get("sample_time")
            sample_time_ms = int(sample_time) * 1000
        else:
            logger.warning(f"⚠️ FOTA系统也无法获取ECU版本信息: {vin}")

    # 从package_global_version中提取完整的车型信息
    original_vehicle_model = vehicle_model
    if package_global_version:
        latest_engineer_version = str(package_global_version.split('*')[0].split('_')[0])
        tmp_latest_bl_version = str(package_global_version.split('*')[-1].split("BL")[-1])
        latest_bl_version = "BL"
        for c in tmp_latest_bl_version:
            if c.isalpha() or c == '_':
                break
            latest_bl_version += c

        # 从package_global_version中提取完整的车型信息（包括G1.1/G1.3等版本）
        detailed_vehicle_model = ('.'.join(package_global_version.split('.')[0:3])).replace('.', ' ', 1)
        logger.info(f"从ECU版本信息中提取的完整车型: '{detailed_vehicle_model}'")

        # 🔧 Dom车型特殊处理：防止Dom车型被错误识别为其他车型
        if is_dom_vehicle and detailed_vehicle_model:
            # 检查ECU版本信息中的车型是否与TVAS返回的Dom车型一致
            detailed_base_name = detailed_vehicle_model.split()[0].lower()
            if detailed_base_name != "dom":
                logger.warning(f"⚠️ Dom车型ECU版本信息异常: ECU中车型='{detailed_vehicle_model}', TVAS中车型='{original_vehicle_model}'")
                logger.warning(f"⚠️ 为防止Dom车型匹配到错误数据，强制使用TVAS车型信息")
                # 强制使用TVAS返回的Dom车型，避免匹配到Blanc等其他车型
                if original_vehicle_model and original_vehicle_model.lower() == "dom":
                    # 使用config中的默认Dom版本
                    complete_model_from_config = get_complete_vehicle_model_from_config(original_vehicle_model)
                    vehicle_model = complete_model_from_config
                    logger.info(f"🔧 Dom车型安全处理: 使用config默认版本 '{vehicle_model}' (忽略ECU中的错误车型: '{detailed_vehicle_model}')")
                else:
                    logger.error(f"❌ Dom车型识别严重错误: TVAS返回的不是Dom车型: '{original_vehicle_model}'")
            else:
                # ECU版本信息中的车型正确，使用它
                vehicle_model = detailed_vehicle_model
                logger.info(f"✅ Dom车型ECU版本信息正确: '{vehicle_model}'")
        elif detailed_vehicle_model and ' G1.' in detailed_vehicle_model:
            # 非Dom车型的正常处理逻辑
            vehicle_model = detailed_vehicle_model
            logger.info(f"🔄 使用ECU版本信息中的完整车型: '{vehicle_model}' (替代TVAS的简化名称: '{original_vehicle_model}')")
        elif original_vehicle_model and ' G1.' not in original_vehicle_model:
            # 如果ECU版本信息中没有提取到完整车型，且原始车型是简化名称，则使用config默认值
            complete_model_from_config = get_complete_vehicle_model_from_config(original_vehicle_model)
            if complete_model_from_config != original_vehicle_model:
                vehicle_model = complete_model_from_config
                logger.warning(f"⚠️ ECU版本信息中未找到完整车型，使用config默认值: '{vehicle_model}' (原始: '{original_vehicle_model}')")
                logger.warning(f"⚠️ 注意：这可能导致Dom车型版本匹配错误，建议检查ECU版本信息")

    # 收集原始ECU数据
    vehicle_platform = vin_values.get("vehicle_platform", "NT3")  # NT3默认值
    raw_ecu_data = collect_raw_ecu_data(vin, ecu_version_list, vehicle_model, vehicle_platform, sample_time_ms)
    
    # 将原始数据添加到全局列表中
    global detail_table_records
    if hasattr(detail_table_records, 'extend'):
        detail_table_records.extend(raw_ecu_data)
    else:
        if not hasattr(process_single_vehicle, '_raw_ecu_records'):
            process_single_vehicle._raw_ecu_records = []
        process_single_vehicle._raw_ecu_records.extend(raw_ecu_data)

    # 根据品牌和具体车型选择对应的版本
    if brand == "alps":
        # NT3-ONVO品牌车型根据具体车型选择版本
        # 提取车型基础名称（去掉版本信息）
        vehicle_base_name = original_vehicle_model.split()[0].lower() if original_vehicle_model else ""

        if vehicle_base_name == "dom":
            package_name = config.DOM_VERSION
            logger.info(f"✅ {vehicle_model}车型使用版本: {package_name}")
        elif vehicle_base_name == "blanc":
            package_name = config.BLANC_VERSION
            logger.info(f"✅ {vehicle_model}车型使用版本: {package_name}")
        elif vehicle_base_name == "rosa":
            package_name = config.ROSA_VERSION
            logger.info(f"✅ {vehicle_model}车型使用版本: {package_name}")
        else:
            # 默认使用DOM_VERSION作为兜底
            package_name = config.DOM_VERSION
            logger.warning(f"⚠️ 未知ONVO车型: {original_vehicle_model} (基础名称: {vehicle_base_name}), 使用DOM版本作为兜底: {package_name}")
    else:
        # NT3-NIO品牌车型根据具体车型选择版本
        # 提取车型基础名称（去掉版本信息）
        vehicle_base_name = original_vehicle_model.split()[0].lower() if original_vehicle_model else ""

        if vehicle_base_name == "cetus":
            package_name = config.CETUS_VERSION
            logger.info(f"✅ {vehicle_model}车型使用版本: {package_name}")
        elif vehicle_base_name == "leo":
            package_name = config.LEO_VERSION
            logger.info(f"✅ {vehicle_model}车型使用版本: {package_name}")
        else:
            # 其他NIO车型使用LEO_VERSION作为兜底
            package_name = config.LEO_VERSION
            logger.info(f"✅ 其他NIO车型 {original_vehicle_model} (基础名称: {vehicle_base_name}) 使用Leo版本: {package_name}")

    # 从 SAM 上获取软硬件兼容表，使用完整的车型信息进行匹配
    hardware_compatibility_dict = None
    vehicle_package_detail = get_package_detail(brand, package_name, vehicle_model)

    if vehicle_package_detail and vehicle_package_detail != "NO_SW_CONFIG":
        hardware_compatibility_dict = parse_nt3_package_detail(vehicle_package_detail)
        logger.debug(json.dumps(hardware_compatibility_dict))
    elif vehicle_package_detail == "NO_SW_CONFIG":
        logger.warning(f"NT3车型 {vehicle_model} 找到了VFR但没有对应的软件配置包，将记录基本信息")
        # 记录基本信息，表明找到了车型但没有详细配置
        update_summary_table_record_dict(
            {
                "VIN": vin,
                "软件包工程版本": f"{vehicle_model} {package_name}",
                "版本迭代号": package_name,
                "硬件是否都兼容": "待确认",
                "各ECU软件都对齐至整车版本": "待确认",
                "采样时间": None,
                "备注": f"找到 {brand} 品牌 {vehicle_model} 车型的VFR，但暂无软件配置包详情"
            },
            vin_values.get("record_id")
        )
        return
    else:
        logger.error(f"Failed to get package detail for brand={brand}, package_name={package_name}, vehicle_model={vehicle_model}")
        update_summary_table_record_dict(
            {
                "VIN": vin,
                "软件包工程版本": "未知",
                "版本迭代号": "未知",
                "硬件是否都兼容": "不确定",
                "各ECU软件都对齐至整车版本": "不确定",
                "采样时间": None,
                "备注": f"无法获取 {brand} 品牌 {vehicle_model} 车型的软件包详情"
            },
            vin_values.get("record_id")
        )
        return

    # ECU版本信息已在前面获取，这里直接使用

    if ecu_version_list is None:
        logger.error(f"❌ 无法获取ECU版本信息: {vin}")
        logger.error(f"   车型: {vehicle_model}, 品牌: {brand}")
        logger.error(f"   已尝试TVAS和FOTA系统，均无法获取数据")
        logger.error(f"   这是Dom车型没有完整匹配日志的主要原因之一")
        update_summary_table_record_dict(
            {
                "VIN": vin,
                "软件包工程版本": "未知",
                "版本迭代号": "未知",
                "硬件是否都兼容": "不确定",
                "各ECU软件都对齐至整车版本": "不确定",
                "采样时间": None,
                "备注": f"获取不到 ECU 的软硬件零件号 (车型: {vehicle_model})"
            },
            vin_values.get("record_id")
        )
        return

    if package_global_version:
        one_record_dict = {
            "VIN": vin,
            "软件包工程版本": latest_engineer_version,
            "版本迭代号": latest_bl_version,
            "目标迭代号": package_name,
            "硬件是否都兼容": "兼容",
            "各ECU软件都对齐至整车版本": "已对齐",
            "采样时间": sample_time_ms,
            "备注": None,
        }
    else:
        one_record_dict = {
            "VIN": vin,
            "软件包工程版本": "空值",
            "版本迭代号": "空值",
            "目标迭代号": package_name,
            "硬件是否都兼容": "兼容",
            "各ECU软件都对齐至整车版本": "已对齐",
            "采样时间": sample_time_ms,
            "备注": "F141 为空，无法获取整车版本号",
        }

    # 确保vin_values包含vehicle_model信息，用于Dom车型的硬件零件号过滤
    if vehicle_model and "vehicle_model" not in vin_values:
        vin_values["vehicle_model"] = vehicle_model

    check_compatibility(hardware_compatibility_dict, ecu_version_list, one_record_dict, vin, vin_values)
    # 在函数最后返回原始数据
    return raw_ecu_data

def get_vehicle_model_platform(vin: str, vin_values: dict):
    global get_tvas_info_request

    res_dict_1 = get_tvas_info_request.query_by_vin(vin)
    if res_dict_1:
        vehicle_model = res_dict_1.get("vehicle_model")
        platform = res_dict_1.get("platform")

        # 如果是NT2.5车型，修改平台标识以便在飞书中单独统计
        if platform and "NT2" in platform and is_nt25_vehicle(vehicle_model):
            platform = "NT2.5"

        update_summary_table_record_dict(
            {
                "车型": vehicle_model,
                "车型平台": platform,
            },
            vin_values.get("record_id")
        )
        return vehicle_model, platform
    else:
        logger.warning(f"Failed to get vehicle_model and platform for {vin}")
        return None, None


def did_info_upload(vehicles_info: dict):
    global fota_onvo_stg
    global fota_nio_stg

    onvo_vids = []
    nio_vids = []
    for vin, values in vehicles_info.items():
        vehicle_model = values.get("vehicle_model")
        if vehicle_model and vehicle_model.lower() in ["dom", "blanc", "rosa"]:
            vehicle_info_dict = fota_onvo_stg.query_vehicle_info(vin)
            if vehicle_info_dict and vehicle_info_dict.get("vid"):
                onvo_vids.append(vehicle_info_dict.get("vid"))
        elif vehicle_model and vehicle_model.lower() in ["leo", "cetus"]:
            if skip_leo:
                continue
            vehicle_info_dict = fota_nio_stg.query_vehicle_info(vin)
            if vehicle_info_dict and vehicle_info_dict.get("vid"):
                nio_vids.append(vehicle_info_dict.get("vid"))

    if onvo_vids:
        if fota_onvo_stg.send_ota_cmd(onvo_vids):
            logger.debug(f"Send OTA command to {onvo_vids}")
        else:
            logger.warning(f"Failed to send OTA command to {onvo_vids}")
    if nio_vids:
        if fota_nio_stg.send_ota_cmd(nio_vids):
            logger.debug(f"Send OTA command to {nio_vids}")
        else:
            logger.warning(f"Failed to send OTA command to {nio_vids}")

    if not onvo_vids and not nio_vids:
        logger.info("No VID found, skip OTA command sending.")


def process_vehicles_concurrently(vehicle_info_dict: dict, max_workers: int = 8):
    """并发处理车辆信息 - 针对i7-7700优化"""
    logger.info(f"开始并发处理 {len(vehicle_info_dict)} 辆车，最大并发数: {max_workers}")

    all_summary_records = []
    all_detail_records = []
    all_raw_ecu_records = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_vin = {
            executor.submit(process_single_vehicle, vin, vin_values): vin
            for vin, vin_values in vehicle_info_dict.items()
        }

        completed_count = 0
        total_count = len(future_to_vin)

        for future in as_completed(future_to_vin):
            vin = future_to_vin[future]
            completed_count += 1

            try:
                vin_result, summary_record, detail_records, raw_ecu_data = future.result()
                logger.info(f"📊 进度: {completed_count}/{total_count} - 完成车辆 {vin_result}")

                if summary_record:
                    all_summary_records.extend(summary_record.values())
                if detail_records:
                    all_detail_records.extend(detail_records)
                if raw_ecu_data:
                    all_raw_ecu_records.extend(raw_ecu_data)

            except Exception as e:
                logger.error(f"并发处理车辆 {vin} 时发生异常: {str(e)}")
                logger.error(f"异常类型: {type(e).__name__}")
                import traceback
                logger.error(f"异常堆栈: {traceback.format_exc()}")

    logger.info(f"并发处理完成: 汇总记录 {len(all_summary_records)} 条, 详细记录 {len(all_detail_records)} 条, 原始ECU记录 {len(all_raw_ecu_records)} 条")
    return all_summary_records, all_detail_records, all_raw_ecu_records

def ecu_version_checker():
    if config.DEBUG:
        logger.info("In debug model.")
    else:
        now = datetime.datetime.now()
        if now.hour != 3:
            logger.debug("Only run ecu_version_checker function at 03:00!")
            return

    summary_table = vehicle_sw_version_table.Summary()
    vehicle_info_dict = summary_table.get_vehicle_info_dict()
    did_info_upload(vehicle_info_dict)

    detail_table = vehicle_sw_version_table.VehicleDetail()
    # 删除各车详情中的记录
    all_record_ids = detail_table.get_all_record_ids(skip_leo)
    detail_table.batch_delete(all_record_ids)

    # 清空原始ECU数据表
    try:
        raw_ecu_table = vehicle_sw_version_table.RawEcuData()
        all_raw_record_ids = raw_ecu_table.get_all_record_ids()
        if all_raw_record_ids:
            logger.info(f"清空原始ECU数据表中的 {len(all_raw_record_ids)} 条记录")
            raw_ecu_table.batch_delete(all_raw_record_ids)
    except Exception as e:
        logger.error(f"清空原始ECU数据表失败: {e}")

    # 使用并发处理替代串行处理
    start_time = time.time()
    # 根据系统配置和车辆数量动态调整并发数
    if SYSTEM_OPTIMIZED:
        optimal_workers = system_config.get_optimal_workers(len(vehicle_info_dict), 'io_intensive')
        recommended = system_config.get_recommended_settings(len(vehicle_info_dict))
        logger.info(f"系统优化配置 - 车辆数量: {len(vehicle_info_dict)}, 并发数: {optimal_workers}")
        logger.info(f"预计处理时间: {recommended['estimated_time']:.1f}秒, 内存使用: {recommended['memory_usage_mb']}MB")
    else:
        # 根据i7-7700 4核8线程CPU，设置合适的并发数
        optimal_workers = min(8, max(4, len(vehicle_info_dict) // 10))  # 动态调整，最少4个，最多8个
        logger.info(f"默认配置 - CPU: i7-7700, 车辆数量: {len(vehicle_info_dict)}, 并发数: {optimal_workers}")

    all_summary_records, all_detail_records, all_raw_ecu_records = process_vehicles_concurrently(vehicle_info_dict, max_workers=optimal_workers)
    processing_time = time.time() - start_time
    logger.info(f"车辆处理总耗时: {processing_time:.2f} 秒")

    # 批量更新数据库
    try:
        if all_summary_records:
            # 过滤掉无效的记录
            valid_summary_records = []
            for record in all_summary_records:
                record_id = record.get("record_id")
                if record_id and record_id.strip():
                    valid_summary_records.append(record)
                else:
                    logger.warning(f"Skipping invalid summary record with empty record_id: {record}")

            if valid_summary_records:
                logger.info(f"批量更新 {len(valid_summary_records)} 条汇总记录")
                summary_table = vehicle_sw_version_table.Summary()
                update_success = summary_table.batch_update(valid_summary_records)
                if not update_success:
                    logger.warning("部分汇总记录更新失败")
            else:
                logger.warning("没有有效的汇总记录需要更新")

        if all_detail_records:
            logger.info(f"批量创建 {len(all_detail_records)} 条详细记录")
            # 确保数据格式正确
            detail_records_formatted = []
            for record in all_detail_records:
                if isinstance(record, dict) and "fields" not in record:
                    # 如果record本身就是fields内容，需要包装
                    detail_records_formatted.append({"fields": record})
                else:
                    # 如果已经有fields结构，直接使用
                    detail_records_formatted.append(record)
            
            detail_table = vehicle_sw_version_table.VehicleDetail()
            create_success = detail_table.batch_create(detail_records_formatted)
            if not create_success:
                logger.warning("部分详细记录创建失败")

        # 批量插入原始ECU数据
        if all_raw_ecu_records:
            logger.info(f"批量插入 {len(all_raw_ecu_records)} 条原始ECU记录")
            try:
                # 确保原始ECU数据格式正确
                raw_ecu_records_formatted = []
                for record in all_raw_ecu_records:
                    if isinstance(record, dict) and "fields" not in record:
                        # 如果record本身就是fields内容，需要包装
                        raw_ecu_records_formatted.append({"fields": record})
                    else:
                        # 如果已经有fields结构，直接使用
                        raw_ecu_records_formatted.append(record)
                
                raw_ecu_table = vehicle_sw_version_table.RawEcuData()
                create_success = raw_ecu_table.batch_create(raw_ecu_records_formatted)
                if create_success:
                    logger.info("✅ 原始ECU数据插入成功")
                else:
                    logger.error("❌ 原始ECU数据批量插入失败，尝试逐条插入")
                    # 尝试逐条插入
                    success_count = 0
                    for record in raw_ecu_records_formatted:
                        try:
                            fields_data = record.get("fields", record)
                            if raw_ecu_table.create_record(fields_data):
                                success_count += 1
                        except Exception as e:
                            logger.error(f"插入单条原始ECU记录失败: {e}")
                    logger.info(f"逐条插入完成，成功 {success_count}/{len(raw_ecu_records_formatted)} 条")
            except Exception as e:
                logger.error(f"原始ECU数据插入过程中发生异常: {e}")
                import traceback
                logger.error(f"异常堆栈: {traceback.format_exc()}")
        else:
            logger.warning("⚠️ 没有原始ECU数据需要插入")

    except Exception as e:
        logger.error(f"批量更新数据库时发生错误: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")

    vehicle_count = len(vehicle_info_dict)
    msg = f"共 {vehicle_count} 车的软硬件零件号同步完成!"
    logger.info(msg)

    # 打印性能统计信息
    print_performance_stats()

    robot.set_payload("[软硬件零件号]运行正常", msg)
    robot.request()


def get_trigger_time():
    now = datetime.datetime.now()
    # 设置为每天凌晨3点运行
    today_3am = datetime.datetime(now.year, now.month, now.day, 3, 0, 0)

    # 如果当前时间已经过了今天的3点，则设置为明天的3点
    if now.hour >= 3:
        today_3am = today_3am + datetime.timedelta(days=1)

    time_list = [today_3am.timestamp()]
    time_str_list = [datetime.datetime.strftime(today_3am, "%Y-%m-%d %H:%M:%S")]

    logger.info(f"Program will be triggered at {time_str_list}")
    return time_list

def collect_raw_ecu_data(vin: str, ecu_version_list: list, vehicle_model: str, vehicle_platform: str, sample_time_ms: int):
    """收集原始ECU版本数据，不进行兼容性对比"""
    raw_ecu_records = []
    
    for ecu_info in ecu_version_list:
        ecu_name = ecu_info.get("ecu", "")
        software_pn = ecu_info.get("software_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())
        hardware_pn = ecu_info.get("hardware_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())
        
        raw_record = {
            "VIN": vin,
            "车型平台": vehicle_platform,
            "车型": vehicle_model,
            "ECU": ecu_name,
            "当前硬件零件号": hardware_pn,
            "当前软件零件号": software_pn,
            "更新时间": sample_time_ms
        }
        raw_ecu_records.append(raw_record)
    
    return raw_ecu_records

def collect_raw_ecu_data(vin: str, ecu_version_list: list, vehicle_model: str, vehicle_platform: str, sample_time_ms: int):
    """收集原始ECU版本数据，不进行兼容性对比"""
    raw_ecu_records = []
    
    for ecu_info in ecu_version_list:
        ecu_name = ecu_info.get("ecu", "")
        software_pn = ecu_info.get("software_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())
        hardware_pn = ecu_info.get("hardware_pn", "").strip(b"\x00".decode()).strip(b"\x02".decode())
        
        raw_record = {
            "VIN": vin,
            "车型平台": vehicle_platform,
            "车型": vehicle_model,
            "ECU": ecu_name,
            "当前硬件零件号": hardware_pn,
            "当前软件零件号": software_pn,
            "更新时间": sample_time_ms
        }
        raw_ecu_records.append(raw_record)
    
    return raw_ecu_records

if __name__ == "__main__":
    dir_path = os.path.dirname(__file__)
    robot = robot_request.RebotRequest()

    summary_table_record_dict = {}
    detail_table_records = []
    skip_leo = False

    try:
        while True:
            url = "https://neep.nioint.com/#/artifact/release/vfr"
            element_id = "app"
            login = login_nio.LoginNio(url, element_id)
            cookie_dict = login.get_cookie()
            sam = get_sam_info.GetSamInfo(cookie_dict)

            # url = "https://vms-alps-stg.nioint.com/#/monitor/realTime"
            # element_id = "wm_div_id"
            # login = login_nio.LoginNio(url, element_id)
            # cookie_dict = login.get_cookie()
            # vms = get_vms_info.GetVMSInfo(cookie_dict)

            url = "https://phoenix.nioint.com/#/fr/cfr"
            element_id = "wm_div_id"
            login = login_nio.LoginNio(url, element_id)
            cookie_dict = login.get_cookie()
            phoenix = get_phoenix_info.GetPhoenixInfo(cookie_dict)

            url = "https://tvas.nioint.com/tvas/vehicleResource"
            element_id = "container"
            login = login_nio.LoginNio(url, element_id)
            cookie_dict = login.get_cookie()
            get_tvas_info_request = get_tvas_info.GetTvasInfo(cookie_dict)

            url = "https://fota-web-onvo-stg.nioint.com/v2/#/fota/vehicles"
            element_id = "app"
            login = login_nio.LoginNio(url, element_id)
            cookie_dict = login.get_cookie()
            cookie_str = "; ".join([str(x) + "=" + str(y) for x, y in cookie_dict.items()])
            fota_onvo_stg = get_fota_info.GetFotaInfo(cookie_str, "https://fota-web-onvo-stg.nioint.com/")

            fota_nio_stg = get_fota_info.GetFotaInfo(config.FOTA_COOKIE, "https://fota-web-stg.nioint.com/")
            if not fota_nio_stg.vehicles_query():
                skip_leo = True
                warning_msg = f"https://fota-web-stg.nioint.com 登录失败，请检查 Cookie 是否过期！本次无法获取 LEO 当前的 ECU 版本号。请更新 Cookie 后重试。"
                logger.warning(warning_msg)
                robot.set_payload("[软硬件零件号]Cookie 过期", warning_msg)
                robot.request()

            if config.DEBUG:
                ecu_version_checker()

            today_str = datetime.date.today().strftime("%Y%m%d")
            trigger_times = get_trigger_time()
            now_timestamp = time.time()  # float
            if now_timestamp < trigger_times[0] or now_timestamp > trigger_times[-1]:
                logger.debug(f"Sleeping at night zzz...\n")
                time.sleep(3600)
                continue

            scheduler = sched.scheduler(time.time, time.sleep)
            # 如果当前时间超过了（大于） 触发时间，则不再触发
            for trigger_time in trigger_times:
                if trigger_time > now_timestamp - 60:  # 60 秒余量
                    scheduler.enterabs(trigger_time, 1, ecu_version_checker)
            scheduler.run()

    except KeyboardInterrupt:
        logger.info("Close script.")



























