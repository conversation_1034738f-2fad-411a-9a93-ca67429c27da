import os
import datetime
import openpyxl


class Spreadsheet:
    """Excel表格基础操作类，提供通用的Excel文件管理功能"""
    
    def __init__(self, path: str):
        """
        初始化Excel操作对象
        
        参数:
            path: Excel文件路径，如果文件不存在则自动创建
        """
        # 检查文件是否存在，不存在则创建新文件
        if not os.path.isfile(path):
            wb = openpyxl.Workbook()  # 创建新工作簿
            wb.save(path)             # 保存到指定路径
            wb.close()                # 关闭工作簿
        
        self.path = path                  # 保存文件路径
        self.wb = openpyxl.load_workbook(self.path)  # 加载工作簿
        self.sheet = None                # 当前操作的工作表，初始化为None

    def get_header_list(self, header_row_num=1, default_header="ECU") -> list:
        """
        获取指定行的表头信息列表
        
        参数:
            header_row_num: 表头所在行号，默认第1行
            default_header: 当表头为空时使用的默认值
        
        返回:
            表头信息列表
        """
        header_list = []
        # 获取表头所在行的所有单元格
        header_row = self.sheet[header_row_num]
        
        for cell in header_row:
            header_name = cell.value
            # 处理空表头，使用默认值
            if header_name is None:
                header_name = default_header
            header_list.append(header_name)
        
        return header_list

    def close_file(self):
        """保存并关闭Excel文件，确保修改被持久化"""
        self.wb.save(self.path)  # 保存文件
        self.wb.close()          # 关闭工作簿

    def remove_other_sheets(self):
        """删除除第一个工作表外的所有工作表，只保留第一个工作表"""
        sheet_names = self.wb.sheetnames
        # 从第二个工作表开始删除（索引1及以后）
        for sheet_name in sheet_names[1:]:
            self.wb.remove(self.wb[sheet_name])
        
        self.close_file()  # 保存修改


class DID(Spreadsheet):
    """车辆诊断数据解析类，继承自Spreadsheet，专注于提取车辆DID相关信息"""
    
    def __init__(self, path: str):
        """
        初始化DID解析器
        
        参数:
            path: 包含车辆诊断数据的Excel文件路径
        """
        super().__init__(path)  # 调用父类构造方法

    def get_vehicle_model(self) -> str:
        """
        从Excel中提取车辆型号信息
        
        返回:
            车辆型号字符串，如果未找到则返回None
        """
        # 切换到"车辆及 BD2 账户信息"工作表
        self.sheet = self.wb['车辆及 BD2 账户信息']
        
        # 遍历工作表中的所有行（从第1行开始）
        for row in self.sheet.iter_rows(min_row=1, values_only=True):
            key = row[0]    # 第1列作为键
            value = row[1]  # 第2列作为值
            
            # 查找"型号"对应的取值
            if key == "型号":
                return value
        
        return None  # 未找到型号信息

    def get_ecu_version(self) -> dict:
        """
        提取ECU（电子控制单元）的版本信息
        
        返回:
            包含以下键的字典:
                - sample_time: 报告生成时间戳
                - ecu_version_map: 各ECU的版本信息映射
                - package_global_version: 全局软件包版本
        """
        sample_time = None  # 报告生成时间戳
        
        # 从"车辆及 BD2 账户信息"工作表获取报告生成时间
        self.sheet = self.wb['车辆及 BD2 账户信息']
        for row in self.sheet.iter_rows(min_row=1, values_only=True):
            key = row[0]
            value = row[1]
            
            if key == "报告生成时间":
                # 将字符串时间转换为时间戳
                sample_time = datetime.datetime.strptime(
                    value, '%Y/%m/%d %H:%M'
                ).timestamp()
                break
        
        ecu_version_map = {}          # 存储各ECU的版本信息
        package_global_version = None  # 全局软件包版本
        
        # 切换到"标识信息"工作表解析ECU数据
        self.sheet = self.wb['标识信息']
        # 从第2行开始遍历（第1行为表头）
        for row in self.sheet.iter_rows(min_row=2, values_only=True):
            # 第1列是ECU名称，取空格前的部分作为ECU标识
            ecu = row[0].split()[0] if row[0] else None
            key = row[2]   # 第3列是DID标识（如F110、F118等）
            value = row[3] # 第4列是标识对应的值
            
            # 处理F141标识（通常用于存储全局版本信息）
            if key == "F141":
                original_value = row[4]  # 第5列是原始值
                # 提取十六进制字符串（去掉前6个字符）
                hex_string = original_value[6:]
                
                # 过滤全F的无效值（F在十六进制中通常表示未定义）
                if not all(c.lower() == 'f' for c in hex_string):
                    # 将十六进制字符串按2个字符一组拆分
                    hex_pairs = [hex_string[i:i+2] for i in range(0, len(hex_string), 2)]
                    # 将每组十六进制转换为ASCII字符
                    ascii_string = ''.join(chr(int(pair, 16)) for pair in hex_pairs)
                    package_global_version = ascii_string
            
            # 只处理有有效数据的行
            if key and value and ecu:
                # 如果ECU不在映射表中，初始化其信息字典
                if ecu not in ecu_version_map:
                    ecu_version_map[ecu] = {"ecu": ecu}
                
                # 处理F110标识（硬件零件号）
                if key == "F110":
                    ecu_version_map[ecu]["hardware_pn"] = value
                # 处理F118标识（软件零件号）
                elif key == "F118":
                    ecu_version_map[ecu]["software_pn"] = value
        
        # 整理并返回所有提取的信息
        return {
            "sample_time": sample_time,
            "ecu_version_map": ecu_version_map,
            "package_global_version": package_global_version
        }
    