import os
import datetime
import openpyxl


class Spreadsheet:
    def __init__(self, path: str):
        if not os.path.isfile(path):
            wb = openpyxl.Workbook()
            wb.save(path)
            wb.close()

        self.path = path
        self.wb = openpyxl.load_workbook(self.path)
        self.sheet = None

    def get_header_list(self, header_row_num=1, default_header="ECU") -> list:
        header_list = []
        header_row = self.sheet[header_row_num]
        for i in header_row:
            header_name = i.value
            if header_name is None:
                header_name = default_header
            header_list.append(header_name)
        return header_list

    def close_file(self):
        self.wb.save(self.path)
        self.wb.close()

    def remove_other_sheets(self):
        sheet_names = self.wb.sheetnames
        for sheet_name in sheet_names[1:]:
            self.wb.remove(self.wb[sheet_name])

        self.close_file()


class DID(Spreadsheet):
    def __init__(self, path: str):
        super().__init__(path)

    def get_vehicle_model(self) -> str:
        self.sheet = self.wb['车辆及 BD2 账户信息']
        for row in self.sheet.iter_rows(min_row=1, values_only=True):
            key = row[0]
            value = row[1]
            if key == "型号":
                return value

    def get_ecu_version(self) -> dict:
        sample_time = None
        self.sheet = self.wb['车辆及 BD2 账户信息']
        for row in self.sheet.iter_rows(min_row=1, values_only=True):
            key = row[0]
            value = row[1]
            if key == "报告生成时间":
                sample_time = datetime.datetime.strptime(value, '%Y/%m/%d %H:%M').timestamp()
                break

        ecu_version_map = {}
        package_global_version = None
        self.sheet = self.wb['标识信息']
        for row in self.sheet.iter_rows(min_row=2, values_only=True):
            ecu = row[0].split()[0]
            key = row[2]
            value = row[3]

            if key == "F141":
                original_value = row[4]
                hex_string = original_value[6:]
                if not all(c.lower() == 'f' for c in hex_string):
                    hex_pairs = [hex_string[i:i + 2] for i in range(0, len(hex_string), 2)]
                    ascii_string = ''.join(chr(int(pair, 16)) for pair in hex_pairs)
                    package_global_version = ascii_string

            if key and value and ecu:
                if ecu not in ecu_version_map.keys():
                    ecu_version_map[ecu] = {"ecu": ecu}

                if key == "F110":
                    ecu_version_map[ecu]["hardware_pn"] = value
                elif key == "F118":
                    ecu_version_map[ecu]["software_pn"] = value

        ecu_did = {
            "sample_time": sample_time,
            "ecu_version_map": ecu_version_map,
            "package_global_version": package_global_version
        }
        return ecu_did
