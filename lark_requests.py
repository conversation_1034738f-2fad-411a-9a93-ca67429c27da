import json
import threading

import requests
from typing import Optional

import config
from log_config import logger


class GetAccessToken:
    def __init__(self):
        self.payload = json.dumps({
            "app_id": config.APP_ID,
            "app_secret": config.APP_SECRET,
        })

    def get_tenant_access_token(self) -> Optional[str]:
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        headers = {
            'Content-Type': 'application/json'
        }
        response = requests.request("POST", url, headers=headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 0 and response_dict.get("msg") == "ok":
                tenant_access_token = response_dict.get("tenant_access_token")
                if tenant_access_token:
                    return tenant_access_token

            logger.error(response_dict)
            return None

        logger.error(f"Failed to get tenant_access_token!")
        logger.error(response.text)
        return None


def timeout(seconds):
    def decorator(func):
        def wrapper(*args, **kwargs) -> Optional[dict]:
            result = None

            def new_func():
                nonlocal result
                result = func(*args, **kwargs)

            t = threading.Thread(target=new_func)
            t.start()
            t.join(seconds)
            if t.is_alive():
                raise TimeoutError(Exception(f"Function timed out after {seconds} seconds"))

            return result

        return wrapper

    return decorator


def response_checker(response, operation: str, msg="success") -> bool:
    if response.ok and response.status_code == 200:
        response_dict = response.json()
        if response_dict.get("code") == 0 and response_dict.get("msg") == msg:
            logger.debug(f"{operation} success.")
            return True
        else:
            # Check if it's a "record not found" error
            error_code = response_dict.get("code")
            error_msg = response_dict.get("msg", "")
            if error_code == 1254043 and "record not found" in error_msg:
                logger.warning(f"{operation} failed: Record not found - {error_msg}")
                return False
            else:
                logger.error(f"{operation} failed: {response_dict}")
                return False

    logger.error(f"{operation} failed: {response.text}")
    return False


def split_list(lst, chunk_size=500):
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


class LarkRequests:
    def __init__(self, app, table):
        self.app = app
        self.table = table
        get_access_token = GetAccessToken()

        self.method = ""
        self.url = ""
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer " + get_access_token.get_tenant_access_token(),
        }
        self.payload = None

    @timeout(10)
    def get_records_handle(self, filter_dict, page_token) -> Optional[list]:
        info = f"Get records from the lark with page_token {page_token}"
        logger.debug(f"{info}...")
        self.method = "POST"
        self.payload = json.dumps(filter_dict)

        if page_token:
            self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                       f"/tables/{self.table}/records/search?page_size=100&page_token={page_token}"
        else:
            self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                       f"/tables/{self.table}/records/search?page_size=100"

        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
                response_data = response_dict.get("data", {})
                logger.debug(f"{info} success.")
                return response_data
            else:
                logger.error(response_dict)
                return None

        logger.error(response.text)
        return None

    def get_records(self, filter_dict=None, page_token=None) -> Optional[dict]:
        if filter_dict is None:
            filter_dict = {}
        try:
            res = self.get_records_handle(filter_dict, page_token)
        except TimeoutError as e:
            logger.error(e)
            return None
        else:
            return res

    def get_all_records(self, filter_dict=None) -> list:
        if filter_dict is None:
            filter_dict = {}
        has_more = True
        page_token = None
        total = 0
        total_item = []
        while has_more:
            logger.debug(f"At page_token: {page_token}")
            response_data = self.get_records(filter_dict, page_token)
            if response_data:
                record_items = response_data.get("items")
                has_more = response_data.get("has_more", False)
                page_token = response_data.get("page_token")
                if record_items:
                    total_item += record_items
                    total = response_data.get("total")
                else:
                    logger.warning(f"Records item is empty at page_token {page_token}!")
            else:
                logger.error(f"Failed to get records at page_token {page_token}!")

        if len(total_item) == total:
            logger.debug(f"Get all {total} records from lark.")
        else:
            logger.error(f"Get {len(total_item)} records from lark != total {total}!")

        return total_item

    def create_record(self, record_dict: dict) -> bool:
        info = "Create record to the lark"
        logger.debug(f"{info}...")
        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records"
        self.payload = json.dumps({
            "fields": record_dict
        })

        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        return response_checker(res, info)

    def update_record(self, record_dict: dict, record_id) -> bool:
        info = f"Update record {record_id} to the lark"
        logger.debug(f"{info}...")
        self.method = "PUT"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/{record_id}"
        self.payload = json.dumps({
            "fields": record_dict
        })

        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        success = response_checker(res, info)
        if not success:
            # Check if it's a record not found error
            if res.ok and res.status_code == 200:
                response_dict = res.json()
                error_code = response_dict.get("code")
                if error_code == 1254043:  # Record not found error
                    logger.warning(f"Record {record_id} not found, it may have been deleted")
        return success

    def delete_record(self, record_id) -> bool:
        info = "Delete record from the lark"
        logger.debug(f"{info}...")
        self.method = "DELETE"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/{record_id}"
        self.payload = ""

        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        return response_checker(res, info)

    def _create_batch(self, batch: list) -> bool:
        """执行单个批次的创建操作"""
        info = f"Batch create {len(batch)} records to the lark"
        logger.debug(f"{info}...")

        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/batch_create"

        self.payload = json.dumps({
            "records": batch
        })

        res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
        return response_checker(res, info)

    def batch_create(self, records: list) -> bool:
        """批量创建记录"""
        if not records:
            return True

        # 调试：打印第一条记录的格式
        if records:
            logger.debug(f"批量创建记录格式检查 - 第一条记录: {records[0]}")
            logger.debug(f"记录总数: {len(records)}")

        # 确保每条记录都有正确的格式
        formatted_records = []
        for record in records:
            if isinstance(record, dict):
                if "fields" in record:
                    formatted_records.append(record)
                else:
                    # 如果没有fields包装，添加包装
                    formatted_records.append({"fields": record})
            else:
                logger.error(f"无效的记录格式: {type(record)} - {record}")
                continue

        # 分批处理
        batch_size = 500
        success_count = 0

        for i in range(0, len(formatted_records), batch_size):
            batch = formatted_records[i:i + batch_size]
            if self._create_batch(batch):
                success_count += len(batch)
            else:
                logger.warning(f"Failed to create batch of {len(batch)} records")

        return success_count == len(formatted_records)

    def batch_update(self, records: list) -> bool:
        """
        example_records = [
            {
                "record_id": "",
                "fields": {}
            },
            {
                "record_id": "",
                "fields": {}
            }
        ]
        """
        info = "Batch update records to the lark"
        logger.debug(f"{info}...")
        if len(records) == 0:
            logger.warning("Records is empty, don't need to update!")
            return True

        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/batch_update"

        sub_lists = split_list(records)
        success_count = 0
        total_count = len(records)

        for sub_list in sub_lists:
            self.payload = json.dumps({
                "records": sub_list
            })

            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            if response_checker(res, info):
                success_count += len(sub_list)
            else:
                # If batch update fails, try individual updates to identify problematic records
                logger.warning(f"Batch update failed for {len(sub_list)} records, trying individual updates...")
                for record in sub_list:
                    record_id = record.get("record_id")
                    fields = record.get("fields", {})
                    if self.update_record(fields, record_id):
                        success_count += 1
                    else:
                        logger.warning(f"Failed to update record {record_id}, it may have been deleted")

        logger.info(f"Successfully updated {success_count}/{total_count} records")
        return success_count > 0  # Return True if at least some records were updated successfully

    def batch_delete(self, record_ids: list) -> bool:
        """
        example_record_ids = [
            "record_id_1",
            "record_id_2",
        ]
        """
        info = "Batch delete records from the lark"
        logger.debug(f"{info}...")
        if len(record_ids) == 0:
            logger.warning("Records is empty, don't need to delete!")
            return True

        self.method = "POST"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}" \
                   f"/tables/{self.table}/records/batch_delete"

        sub_lists = split_list(record_ids)
        for sub_list in sub_lists:
            self.payload = json.dumps({
                "records": sub_list
            })

            res = requests.request(self.method, self.url, headers=self.headers, data=self.payload)
            if not response_checker(res, info):
                return False

        return True

    def get_table_fields(self) -> Optional[dict]:
        """获取表格的字段信息"""
        info = "Get table fields from lark"
        logger.debug(f"{info}...")

        self.method = "GET"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app}/tables/{self.table}/fields"

        res = requests.request(self.method, self.url, headers=self.headers)
        if res.ok and res.status_code == 200:
            response_dict = res.json()
            if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
                fields_data = response_dict.get("data", {})
                logger.debug(f"{info} success.")
                return fields_data
            else:
                logger.error(response_dict)
                return None

        logger.error(res.text)
        return None

