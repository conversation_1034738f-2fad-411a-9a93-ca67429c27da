import requests
from typing import Optional

from log_config import logger


class GetSamInfo:
    def __init__(self, cookie: dict):
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json",
            "X-Fp-App-Id": "101516",
            "X-Fp-Domain": "neep.nioint.com"
        }

    def get_nt3_vfr_list(self, brand: str) -> Optional[list]:
        self.method = "GET"
        self.url = f"https://fp.nioint.com/api/v1/sam/{brand}/vfr?offset=0&limit=20"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("code") == 200:
                results = response_dict.get("data", {}).get("results")
                if results and len(results) > 0:
                    return results

        logger.error(f"Failed to get {brand} vfr list.")
        logger.error(response.text)
        return None

        # """
        # {
        #         "id": 22,
        #         "sw_pn": "V0069900",
        #         "latest_revision": "AP",
        #         "vfi_clone_status": "ToDo",
        #         "vfi_import_status": "ToDo",
        #         "vfi_auto_create_status": "ToDo",
        #         "vfi_auto_create_done_time": null,
        #         "vfi_create_by_jql_status": "ToDo",
        #         "vfi_auto_update_status": "ToDo",
        #         "vfi_clone_links_status": "ToDo",
        #         "vfi_smoke_test_status": "ToDo",
        #         "vfi_quick_test_status": "ToDo",
        #         "vfi_quick_test_done_time": null,
        #         "is_deleted": false,
        #         "created_by": "bo.zhu3",
        #         "created_time": "1713346026",
        #         "updated_time": "1713346179",
        #         "brand": "ALPS",
        #         "platform": "NT3",
        #         "vehicle_type": "Dom G1.1",
        #         "name": "BL0.6.0",
        #         "planned_bp": "BL0.6.0",
        #         "child_version": null,
        #         "sw_engineering_version": "DOM.G1.1.AO.01",
        #         "status": "InProcess",
        #         "prev_status": "Init",
        #         "comment": null,
        #         "notice_links": [],
        #         "notice_users": [],
        #         "notice_emails": [],
        #         "updated_by": "bo.zhu3",
        #         "plm_release_time": "1716134400",
        #         "base_config_time": null,
        #         "freeze_time": null,
        #         "user_release_time": null,
        #         "vehicle_id": 2,
        #         "released_by": "",
        #         "released_time": ""
        #     }
        # """

    def get_nt3_vfr_sw_config_list(self, brand: str, release_plan_id: int) -> Optional[list]:
        self.method = "GET"
        self.url = f"https://fp.nioint.com/api/v1/sam/{brand}/vfr/sw_config_list?release_plan_id={release_plan_id}&offset=0&limit=20"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            logger.debug(f"SAM API响应: {response_dict}")

            if response_dict.get("result_code") == "success" and response_dict.get("code") == 200:
                results = response_dict.get("data", {}).get("results")
                count = response_dict.get("data", {}).get("count", 0)

                if results and len(results) > 0:
                    logger.info(f"✓ 成功获取 {brand} brand, release_plan_id={release_plan_id} 的sw_config_list，共{len(results)}条记录")
                    return results
                else:
                    logger.warning(f"⚠️ {brand} brand, release_plan_id={release_plan_id} 的sw_config_list为空 (count={count})")
                    return None
            else:
                logger.error(f"SAM API返回错误: result_code={response_dict.get('result_code')}, code={response_dict.get('code')}")
                logger.error(f"错误详情: {response_dict}")
                return None
        else:
            logger.error(f"Failed to get {brand} vfr sw_config_list. HTTP {response.status_code}")
            logger.error(response.text)
            return None

        # """
        # {
        #         "id": 395,
        #         "sw_name": "NT3-Dom G1.1-BL0.6.1-2",
        #         "sw_config_name": "RC_2",
        #         "trigger_type": "manual",
        #         "sw_pn": "V0069900",
        #         "base_version": "NT3-Dom G1.1-BL0.6.1-1",
        #         "plm_status": null,
        #         "release_time": "",
        #         "sw_revision": null,
        #         "created_time": 1717380977.04035,
        #         "updated_time": 1717380977.040421,
        #         "created_by": "bo.zhu3",
        #         "package_status": "SUCCESS",
        #         "package_success_time": 1717381496.195801,
        #         "package_request_time": 1717380977.038292,
        #         "vfi_smoking_test_time": null
        #     },
        # """

    def get_package_detail(self, brand: str, sw_config_id: int, base_version: str) -> Optional[list]:
        self.method = "GET"
        self.url = f"https://fp.nioint.com/api/v1/sam/{brand}/vehicle-sw-config/compare?id={sw_config_id}&offset=0" \
                   f"&limit=150&base_version={base_version}&change_type=no_change%2Csw_change%2Chw_change "
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("code") == 200:
                results = response_dict.get("data", {}).get("results")
                if results and len(results) > 0:
                    return results

        logger.error(response.text)
        logger.error("Failed to get vehicle info.")
        return None
        # """
        # {
        #     "pkg_ecu_id": 32005,
        #     "brand": "ALPS",
        #     "ecu_mark": "CDF",
        #     "sub_category": "SW Package",
        #     "sw_pn": "P0335559",
        #     "sw_type": "None",
        #     "sw_part_name": "COCKPIT DOMAIN FUNCTION-SW",
        #     "current_revision": "BH",
        #     "base_revision": "BH",
        #     "sw_change_type": "no_change",
        #     "hw_list": [
        #         {
        #             "ecu_name": "CDF",
        #             "label": "CDF",
        #             "hw_change_type": "no_change",
        #             "children": [
        #                 {
        #                     "label": "P0337429",
        #                     "children": [
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AD",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AE",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AF",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AG",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AH",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         }
        #                     ]
        #                 }
        #             ]
        #         }
        #     ],
        #     "changed_hw_list": [
        #         "+CDF P0337429 AI"
        #     ],
        #     "change_type_list": [
        #         "no_change"
        #     ],
        #     "artifact_id": 12392,
        #     "ecu_sw_id": 2769,
        #     "release_id": 1916,
        #     "pack_mode": "SELF_DEVELOPED",
        #     "md5": "P0335559 BH.tar.gz:687003b7b920825731d3336c457540c6-2997",
        #     "vehicle_type": "Dom G1.1",
        #     "feature_code": null
        # }
        # """
