import requests
from typing import Optional

from log_config import logger

# """
# 第一步：get_nt3_vfr_list(brand) → 获取品牌下所有 VFR 计划，得到 id（release_plan_id）。
# 例：查询 brand="nio" 得到 id=113（对应 Cetus G1.1 车型的 BL1.3.0 计划）。
# 第二步：get_nt3_vfr_sw_config_list(brand, release_plan_id) → 用 release_plan_id 获取该计划的软件配置，得到 id（sw_config_id）和 base_version。
# 例：用 release_plan_id=85 得到 id=1554 和 base_version=NT3-Blanc G1.1-BL0.12.0-13。
# 第三步：get_package_detail(brand, sw_config_id, base_version) → 用 sw_config_id 和 base_version 获取 ECU 详情，得到每个模块的软硬件版本及变更。
# 例：用 id=1554 得到 ACM、ADF 等 ECU 的版本和变更状态。
# """

class GetSamInfo:
    def __init__(self, cookie: dict):
        self.url = ""
        self.method = ""
        self.payload = {}
        self.headers = {
            "Cookie": "; ".join([str(x) + "=" + str(y) for x, y in cookie.items()]),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/118.0.0.0 Safari/537.36",
            "Content-Type": "application/json",
            "X-Fp-App-Id": "101516",
            "X-Fp-Domain": "neep.nioint.com"
        }

    # """
    # get_nt3_vfr_list：获取品牌对应的 VFR（Vehicle Firmware Release）列表
    # 通过品牌（如 nio、alps）调用 https://fp.nioint.com/api/v1/sam/{brand}/vfr 接口，获取该品牌下符合条件的车辆固件发布计划列表，包含固件版本、状态、计划时间等核心信息。
    # 请求构造：
    #     method：GET（无需请求体，参数通过 URL 传递）。
    #     url：拼接品牌参数 brand，固定分页参数 offset=0&limit=20（获取第 1 页，20 条数据）。
    #     示例：https://fp.nioint.com/api/v1/sam/nio/vfr?offset=0&limit=20。
    #     headers：包含三个关键字段（接口要求）：
    #     Cookie：用户身份验证信息（从初始化传入的 cookie 字典转换而来）。
    #     X-Fp-App-Id：固定值 101516（应用标识）。
    #     X-Fp-Domain：固定值 neep.nioint.com（域名标识）。
    # 响应处理：
    #     校验响应有效性：response.ok 且 status_code == 200。
    #     校验业务状态：response_dict["result_code"] == "success" 且 response_dict["code"] == 200。
    #     提取数据：从 response_dict["data"]["results"] 中获取 VFR 列表（若不为空则返回，否则返回 None）。
    
    # 函数直接返回 results 列表，保留原始结构，未做额外处理。
    # 提供品牌下所有固件发布计划的概览，可用于筛选特定车型（如 Cetus G1.1、Rosa G1.1）或状态（如 InProcess）的版本，为后续查询详细配置提供 id（VFR 唯一标识）。
    # """

    def get_nt3_vfr_list(self, brand: str) -> Optional[list]:
        self.method = "GET"
        self.url = f"https://fp.nioint.com/api/v1/sam/{brand}/vfr?offset=0&limit=20"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("code") == 200:
                results = response_dict.get("data", {}).get("results")
                if results and len(results) > 0:
                    return results

        logger.error(f"Failed to get {brand} vfr list.")
        logger.error(response.text)
        return None

        # """
        # {
        #         "id": 22,
        #         "sw_pn": "V0069900",
        #         "latest_revision": "AP",
        #         "vfi_clone_status": "ToDo",
        #         "vfi_import_status": "ToDo",
        #         "vfi_auto_create_status": "ToDo",
        #         "vfi_auto_create_done_time": null,
        #         "vfi_create_by_jql_status": "ToDo",
        #         "vfi_auto_update_status": "ToDo",
        #         "vfi_clone_links_status": "ToDo",
        #         "vfi_smoke_test_status": "ToDo",
        #         "vfi_quick_test_status": "ToDo",
        #         "vfi_quick_test_done_time": null,
        #         "is_deleted": false,
        #         "created_by": "bo.zhu3",
        #         "created_time": "1713346026",
        #         "updated_time": "1713346179",
        #         "brand": "ALPS",
        #         "platform": "NT3",
        #         "vehicle_type": "Dom G1.1",
        #         "name": "BL0.6.0",
        #         "planned_bp": "BL0.6.0",
        #         "child_version": null,
        #         "sw_engineering_version": "DOM.G1.1.AO.01",
        #         "status": "InProcess",
        #         "prev_status": "Init",
        #         "comment": null,
        #         "notice_links": [],
        #         "notice_users": [],
        #         "notice_emails": [],
        #         "updated_by": "bo.zhu3",
        #         "plm_release_time": "1716134400",
        #         "base_config_time": null,
        #         "freeze_time": null,
        #         "user_release_time": null,
        #         "vehicle_id": 2,
        #         "released_by": "",
        #         "released_time": ""
        #     }
        # """

    # """
    # get_nt3_vfr_sw_config_list：获取 VFR 对应的软件配置列表
    # 基于 get_nt3_vfr_list 返回的 id（即 release_plan_id），调用 https://fp.nioint.com/api/v1/sam/{brand}/vfr/sw_config_list 接口，获取该发布计划下的软件配置详情，包含配置名称、打包状态、版本依赖等信息。
    # 请求构造：
    #     method：GET。
    #     url：拼接品牌 brand 和发布计划 ID release_plan_id，固定分页参数 offset=0&limit=20。
    #     示例：https://fp.nioint.com/api/v1/sam/nio/vfr/sw_config_list?release_plan_id=85&offset=0&limit=20。
    #     headers：与 get_nt3_vfr_list 相同（需 Cookie、X-Fp-App-Id、X-Fp-Domain）。

    # 响应处理：
    #     校验响应和业务状态（同前）。
    #     提取数据：从 response_dict["data"]["results"] 获取软件配置列表，同时记录总条数 count 用于日志。
    #     日志处理：成功时输出记录数，为空时警告，失败时记录错误详情。

    # 函数返回 results 列表，保留原始结构，关键信息包括配置 ID（id）和基础版本（base_version），为下一步查询详情提供参数。
    # 关联 VFR 计划与具体软件配置，明确该计划下包含的软件版本、打包状态等，是从「发布计划」到「可执行软件包」的中间桥梁。
    # """
    def get_nt3_vfr_sw_config_list(self, brand: str, release_plan_id: int) -> Optional[list]:
        self.method = "GET"
        self.url = f"https://fp.nioint.com/api/v1/sam/{brand}/vfr/sw_config_list?release_plan_id={release_plan_id}&offset=0&limit=20"
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            logger.debug(f"SAM API响应: {response_dict}")

            if response_dict.get("result_code") == "success" and response_dict.get("code") == 200:
                results = response_dict.get("data", {}).get("results")
                count = response_dict.get("data", {}).get("count", 0)

                if results and len(results) > 0:
                    logger.info(f"✓ 成功获取 {brand} brand, release_plan_id={release_plan_id} 的sw_config_list，共{len(results)}条记录")
                    return results
                else:
                    logger.warning(f"⚠️ {brand} brand, release_plan_id={release_plan_id} 的sw_config_list为空 (count={count})")
                    return None
            else:
                logger.error(f"SAM API返回错误: result_code={response_dict.get('result_code')}, code={response_dict.get('code')}")
                logger.error(f"错误详情: {response_dict}")
                return None
        else:
            logger.error(f"Failed to get {brand} vfr sw_config_list. HTTP {response.status_code}")
            logger.error(response.text)
            return None

        # """
        # {
        #         "id": 395,
        #         "sw_name": "NT3-Dom G1.1-BL0.6.1-2",
        #         "sw_config_name": "RC_2",
        #         "trigger_type": "manual",
        #         "sw_pn": "V0069900",
        #         "base_version": "NT3-Dom G1.1-BL0.6.1-1",
        #         "plm_status": null,
        #         "release_time": "",
        #         "sw_revision": null,
        #         "created_time": 1717380977.04035,
        #         "updated_time": 1717380977.040421,
        #         "created_by": "bo.zhu3",
        #         "package_status": "SUCCESS",
        #         "package_success_time": 1717381496.195801,
        #         "package_request_time": 1717380977.038292,
        #         "vfi_smoking_test_time": null
        #     },
        # """

    # """
    # get_package_detail：获取软件配置的 ECU 软硬件详情
    # 基于 get_nt3_vfr_sw_config_list 返回的 id（即 sw_config_id）和 base_version，调用对比接口 https://fp.nioint.com/api/v1/sam/{brand}/vehicle-sw-config/compare，获取该软件配置与基础版本的差异详情，包括 ECU 模块的软硬件版本、变更类型等。
    # 请求构造：
    #     method：GET。
    #     url：拼接品牌 brand、软件配置 ID sw_config_id、基础版本 base_version，并指定变更类型 change_type=no_change,sw_change,hw_change（查询所有变更类型）。
    #     示例：https://fp.nioint.com/api/v1/sam/alps/vehicle-sw-config/compare?id=1554&base_version=NT3-Blanc...。
    #     headers：与前两个函数相同（需三个关键字段）。
    # 响应处理：
    #     校验响应和业务状态（同前）。
    #     提取数据：从 response_dict["data"]["results"] 获取 ECU 详情列表（包含软硬件变更信息）。
    # 日志处理：
    #     成功时输出记录数，为空时警告，失败时记录错误详情。
    # 函数返回 results 列表，保留原始结构，关键信息包括 ECU 模块的软硬件版本、变更类型等。
    # 关联软件配置与具体 ECU 模块，明确该软件配置在不同 ECU 上的软件版本、硬件变更等，是从「软件配置」到「ECU 模块」的详细桥梁。
    # """
    def get_package_detail(self, brand: str, sw_config_id: int, base_version: str) -> Optional[list]:
        self.method = "GET"
        self.url = f"https://fp.nioint.com/api/v1/sam/{brand}/vehicle-sw-config/compare?id={sw_config_id}&offset=0" \
                   f"&limit=150&base_version={base_version}&change_type=no_change%2Csw_change%2Chw_change "
        response = requests.request(self.method, self.url, headers=self.headers, data=self.payload)

        if response.ok and response.status_code == 200:
            response_dict = response.json()
            if response_dict.get("result_code") == "success" and response_dict.get("code") == 200:
                results = response_dict.get("data", {}).get("results")
                if results and len(results) > 0:
                    return results

        logger.error(response.text)
        logger.error("Failed to get vehicle info.")
        return None
        # """
        # {
        #     "pkg_ecu_id": 32005,
        #     "brand": "ALPS",
        #     "ecu_mark": "CDF",
        #     "sub_category": "SW Package",
        #     "sw_pn": "P0335559",
        #     "sw_type": "None",
        #     "sw_part_name": "COCKPIT DOMAIN FUNCTION-SW",
        #     "current_revision": "BH",
        #     "base_revision": "BH",
        #     "sw_change_type": "no_change",
        #     "hw_list": [
        #         {
        #             "ecu_name": "CDF",
        #             "label": "CDF",
        #             "hw_change_type": "no_change",
        #             "children": [
        #                 {
        #                     "label": "P0337429",
        #                     "children": [
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AD",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AE",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AF",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AG",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         },
        #                         {
        #                             "pn": "P0337429",
        #                             "label": "AH",
        #                             "hw_change_type": "no_change",
        #                             "children": []
        #                         }
        #                     ]
        #                 }
        #             ]
        #         }
        #     ],
        #     "changed_hw_list": [
        #         "+CDF P0337429 AI"
        #     ],
        #     "change_type_list": [
        #         "no_change"
        #     ],
        #     "artifact_id": 12392,
        #     "ecu_sw_id": 2769,
        #     "release_id": 1916,
        #     "pack_mode": "SELF_DEVELOPED",
        #     "md5": "P0335559 BH.tar.gz:687003b7b920825731d3336c457540c6-2997",
        #     "vehicle_type": "Dom G1.1",
        #     "feature_code": null
        # }
        # """
