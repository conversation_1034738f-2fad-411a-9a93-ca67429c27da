# app
APP_ID = "cli_a5807f21ddb8d00e"
APP_SECRET = "t5V8n0IdFF4hboQERYNT6d5AQ1JOnImK"

# 车辆状态信息收集
# https://nio.feishu.cn/base/XuG3bU8uDaR4Nts7geLcTO1Jnhh?table=tblTRg5dtb5cuA1L&view=vewLe6zimv
APP_TOKEN = "XuG3bU8uDaR4Nts7geLcTO1Jnhh"
# 总表 table
TABLE_ID = "tblTRg5dtb5cuA1L"
# 各车详细 table
DETAIL_TABLE_ID = "tblhlTV5si8Ng1Rj"

# 原始ECU版本数据表
RAW_ECU_TABLE_ID = "tblWGJOEMOzcj6cG"

# 机器人信息
WEBHOOK = "https://open.feishu.cn/open-apis/bot/v2/hook/233ea5f2-ed0a-406a-9418-582edd1fb517"
SECRET = "3sQrZERt9Rg1P4yrWu1Gbh"

# 整车对齐版本
NT2_VERSION = "BL3.2.2"        # NT2车型版本
NT25_VERSION = "BL1.1.0"       # NT2.5车型版本 (NIO-NT2.5的整车版本为:BL110)
DOM_VERSION = "BL2.0.0"        # NT3-ONVO Dom车型版本 (NT3-ONVO的Dom车型整车版本为:BL200)
BLANC_VERSION = "BL2.0.0"      # NT3-ONVO Blanc车型版本 (NT3-ONVO的Blanc车型整车版本为:BL200)
ROSA_VERSION = "BL2.0.0"       # NT3-ONVO Rosa车型版本 (NT3-ONVO的Rosa车型整车版本为:BL200)
LEO_VERSION = "BL1.2.0"        # NT3-NIO LEO车型版本 (NT3-NIO的LEO车型整车版本为：BL110)
CETUS_VERSION = "BL0.9.3"      # NT3-NIO CETUS车型版本 (CETUS的整车版本为;BL093)

# GA version
GA_VERSION = {
    # NT2 车型 - 根据页面信息更新
    "Aries": ["Aries G1.1"],
    "ES7": ["ES7 G1.3"],
    "ET5": ["ET5 G1.2", "ET5 G1.F"],  # 添加NT2.5版本
    "ET7": ["ET7 G1.F"],
    "Libra": ["Libra G1.1", "Libra G1.F"],        # 添加NT2.5版本
    "Lyra": ["Lyra G1.1"],
    "Orion": ["Orion G1.2", "Orion G1.F"],        # 添加NT2.5版本
    "Sirius": ["Sirius G1.1", "Sirius G1.F"],     # 添加NT2.5版本

    # NT3-ONVO品牌车型
    "Dom": ["Dom G1.1", "Dom G1.3"],
    "Blanc": ["Blanc G1.1"],
    "Rosa": ["Rosa G1.1"],

    # NT3-NIO品牌车型
    "Leo": ["Leo G1.1"],
    "Cetus": ["Cetus G1.1"],
}

# 登录信息
USER = "lixue.gao"
PASSWORD = "Glx77777yyyy"

# https://fota-web-stg.nioint.com/ cookie
FOTA_COOKIE = "FOTA-LGUSER=lixue.gao; ota-area=CN; ota-sys=; tenant=nio_ota; SEC-TAG=eyJfdSI6ImxpeHVlLmdhbyIsIl90IjoxNzUxOTM4MDczODI2fQ==:5bdf566b8443d1776adb4155ba6695a8; page-gateway-sid-cn-prod=s%3Ank6NdCEMSEjMF9kk2KvEtyLWPzO3SbFI.RB%2F25fviryhNE%2FQXdAyjEAYyPFKjDpORwgBDG%2FDdXVg; page-gateway-sid-plm=s_nk6NdCEMSEjMF9kk2KvEtyLWPzO3SbFI.441ff6e5fbe2af284d13f417740ca31006323c52a30e9391c200431bf0dd5d58; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22lixue.gao%22%2C%22first_id%22%3A%22194a12617a6d65-007c1e9f25ff0448-1f462c6f-2073600-194a12617a7132c%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0YTEyNjE3YTZkNjUtMDA3YzFlOWYyNWZmMDQ0OC0xZjQ2MmM2Zi0yMDczNjAwLTE5NGExMjYxN2E3MTMyYyIsIiRpZGVudGl0eV9sb2dpbl9pZCI6ImxpeHVlLmdhbyJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22lixue.gao%22%7D%2C%22%24device_id%22%3A%22194a1261a6510d2-093b7ccdec79a5-1f462c6f-2073600-194a1261a66128f%22%7D; page-gateway-secure-sid-cn-prod=s%3AoLvVQtIxsyVFza6pIvU2TSnL-HY5MdJO.H7JqFHDUtP8nz%2BiX2mwD%2FJrvBv%2FetBak3OZZggKp04k; page-gateway-secure-sid-plm=s_oLvVQtIxsyVFza6pIvU2TSnL-HY5MdJO.1fb26a1470d4b4ff27cfe897da6c03fc9aef06ffdeb416a4dce6598202a9d389; lang=zh_CN; NIO-FOTA=MTc1MjA1NjMyNHxEdi1CQkFFQ180SUFBUkFCRUFBQV9nR1ZfNElBQ1FaemRISnBibWNNQkFBQ2FXUUdjM1J5YVc1bkRDWUFKRE0wTlRRNE9EVXpMVEV5WkRVdE5ERmxOQzAzWVdNM0xUSTFNemM1TWpFelpqQTBOQVp6ZEhKcGJtY01CQUFDZEdzR2MzUnlhVzVuREQwQU96SXVNRGRMTWtaVVNrdEpRek5CU2tsSVYxQkxOVE5QU0ZGVVJWRktSMEZCTjFnelFreGFRVTFLUlZJM1dWWktTVVpXTXpWWVRWRXRMUzB0Qm5OMGNtbHVad3dFQUFKcGNBWnpkSEpwYm1jTURRQUxNVEF1TVRFeExqTXVNelVHYzNSeWFXNW5EQVFBQW14MEJuTjBjbWx1Wnd3RkFBTlRVMDhHYzNSeWFXNW5EQVFBQW5SbEJuTjBjbWx1Wnd3SkFBZHVhVzlmYjNSaEJuTjBjbWx1Wnd3RUFBSmhjZ1p6ZEhKcGJtY01CQUFDUTA0R2MzUnlhVzVuREFRQUFtTnNCbk4wY21sdVp3eENBRUExTWpjNE56SmpNelF4TldRMU4ySmpPVGsyTUdRek9HSXdNelkyTldRMVpUYzRNamt6WldVeE1UVmlOV0V3Wm1Jd056QmhPRFpqTkdRNU0yWTBaalE1Qm5OMGNtbHVad3dFQUFKemRBVnBiblEyTkFRR0FQelEzSVFJQm5OMGNtbHVad3dFQUFKemVRWnpkSEpwYm1jTUFnQUF8SmxtfgrpHz5scIm5iTMiysP7Imp8qJVT_RQYRpZaQc8=; fota-clientid=c7806c6cba5d28dc9a6ed04869ee987c"

# 是否是 debug 模式 True False
DEBUG = True



